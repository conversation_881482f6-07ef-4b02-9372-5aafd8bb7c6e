import datetime
import platform
import ctypes
from ctypes import wintypes
import csv
import json
import traceback
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QSpinBox, QTabWidget, QSplitter, QFrame, QMenu, QAction, QSizePolicy,
                            QTextBrowser, QFileDialog, QScrollArea)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import (QIcon, QFont, QColor, QPainter, QPixmap, QBrush, QPen,
                        QLinearGradient, QRadialGradient, QTextDocument)
from PyQt5.QtPrintSupport import QPrinter

from database import (Inventory, Supplier, get_session)
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency, format_quantity)
from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel, StyledTabWidget)
from ui.common_dialogs import WarningDialog
from ui.title_bar_utils import TitleBarStyler
from sqlalchemy import func


class DeleteInventoryDialog(QDialog):
    """نافذة حذف عنصر المخزن مشابهة لنافذة حذف العميل"""

    def __init__(self, parent=None, item=None):
        super().__init__(parent)
        self.item = item
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف العميل"""
        self.setWindowTitle("📦 حذف - نظام إدارة المخزن المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("📦 حذف عنصر المخزن")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات العنصر مضغوطة
        if self.item:
            info_text = f"📦 {self.item.name[:15]}{'...' if len(self.item.name) > 15 else ''}"
            if self.item.quantity:
                info_text += f" | 📊 {self.item.quantity:.0f}"
            if self.item.selling_price:
                info_text += f" | 💰 {self.item.selling_price:.0f} ج"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("📦 حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            from PyQt5.QtGui import QRadialGradient
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "📦")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)


class InventoryItemDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل عنصر في المخزون"""

    def __init__(self, parent=None, item=None, session=None):
        super().__init__(parent)
        self.item = item
        self.session = session
        self.parent_widget = parent  # حفظ مرجع للوالد
        self.init_ui()

    def customize_title_bar(self):
        """تخصيص شريط العنوان - مطابق تماماً للعملاء والموردين"""
        try:
            # إنشاء أيقونة مخصصة للمخزن مطابقة للعملاء والموردين
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج مطابق للعملاء والموردين
            from PyQt5.QtGui import QLinearGradient
            gradient = QLinearGradient(0, 0, 48, 48)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق
            gradient.setColorAt(0.5, QColor(147, 51, 234))  # بنفسجي
            gradient.setColorAt(1, QColor(236, 72, 153))  # وردي

            # رسم دائرة متدرجة
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)

            # رسم رمز المخزن
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "📦")

            painter.end()

            # تعيين الأيقونة
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم متطور على شريط العنوان
            self.apply_advanced_title_bar_styling()

            # توسيط النص في شريط العنوان
            self.center_title_text()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)

    def center_title_text(self):
        """تحسين وضع النص في منتصف شريط العنوان"""
        try:
            # إضافة مسافات لتوسيط النص بصرياً
            original_title = "📦 تعديل عنصر المخزون - نظام إدارة المخزون المتطور والشامل" if self.item else "📦 إضافة عنصر جديد للمخزون - نظام إدارة المخزون المتطور والشامل"

            # حساب المسافات المطلوبة للتوسيط
            padding_spaces = "    "  # مسافات إضافية للتوسيط
            centered_title = f"{padding_spaces}{original_title}{padding_spaces}"

            # تحديث العنوان مع التوسيط
            self.setWindowTitle(centered_title)

        except Exception as e:
            print(f"تحذير: فشل في توسيط النص: {e}")

    def style_advanced_button(self, button, color_scheme='primary', has_menu=False):
        """تطبيق تصميم متطور للأزرار - مطابق للعملاء والموردين"""
        try:
            # ألوان مختلفة للأزرار
            color_schemes = {
                'primary': {
                    'base': '#2563EB',
                    'hover': '#1D4ED8',
                    'pressed': '#1E40AF',
                    'shadow': 'rgba(37, 99, 235, 0.4)'
                },
                'emerald': {
                    'base': '#10B981',
                    'hover': '#059669',
                    'pressed': '#047857',
                    'shadow': 'rgba(16, 185, 129, 0.4)'
                },
                'danger': {
                    'base': '#EF4444',
                    'hover': '#DC2626',
                    'pressed': '#B91C1C',
                    'shadow': 'rgba(239, 68, 68, 0.4)'
                }
            }

            colors = color_schemes.get(color_scheme, color_schemes['primary'])

            # مؤشر القائمة المنسدلة
            menu_indicator = ""
            if has_menu:
                menu_indicator = """
                    QPushButton::menu-indicator {
                        image: none;
                        width: 0px;
                    }
                """

            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {colors['base']}, stop:0.3 {colors['hover']},
                        stop:0.7 {colors['base']}, stop:1 {colors['pressed']});
                    color: white;
                    border: 3px solid {colors['pressed']};
                    border-radius: 12px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 4px 12px {colors['shadow']};
                    min-height: 35px;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {colors['hover']}, stop:0.3 {colors['pressed']},
                        stop:0.7 {colors['hover']}, stop:1 {colors['base']});
                    border: 3px solid {colors['base']};
                    transform: translateY(-2px) scale(1.03);
                    box-shadow: 0 6px 20px {colors['shadow']},
                               0 3px 12px rgba(0, 0, 0, 0.4);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {colors['pressed']}, stop:0.3 {colors['hover']},
                        stop:0.7 {colors['pressed']}, stop:1 {colors['base']});
                    transform: translateY(1px) scale(0.98);
                    box-shadow: 0 2px 8px {colors['shadow']};
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            pass  # خطأ في تطبيق تصميم الزر

    def init_ui(self):
        # استخدام شريط العنوان الطبيعي للنظام مع النص في المنتصف - مطابق للعملاء والموردين
        self.setWindowTitle("📦 تعديل عنصر المخزون - نظام إدارة المخزون المتطور والشامل" if self.item else "📦 إضافة عنصر جديد للمخزون - نظام إدارة المخزون المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان - مطابق للعملاء والموردين
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان - مطابق للعملاء والموردين
        self.customize_title_bar()

        self.setModal(True)
        self.resize(650, 650)  # جعل النافذة مربعة مطابق للعملاء والموردين

        # خلفية النافذة مطابقة تماماً للعملاء والموردين
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # إنشاء التخطيط الرئيسي مطابق للعملاء والموردين
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # إضافة عنوان النافذة الداخلي مطابق للعملاء والموردين
        title_text = "تعديل بيانات عنصر المخزون" if self.item else "إضافة عنصر جديد للمخزون"
        title_label = QLabel(f"📦 {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 18px 25px;
                margin: 8px 5px;
                font-weight: bold;
                font-size: 18px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4),
                           0 3px 12px rgba(37, 99, 235, 0.3);
                min-height: 50px;
                max-height: 50px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء نموذج معلومات العنصر مطابق للعملاء والموردين
        from ui.unified_styles import StyledGroupBox
        form_group = StyledGroupBox("📦 معلومات العنصر")

        # تخطيط النموذج مطابق للعملاء والموردين
        form_layout = QFormLayout()

        # دالة إنشاء تسمية مصممة مطابقة للعملاء والموردين
        def create_styled_label(text, icon="", required=False):
            label_text = f"{icon} {text}" if icon else text
            if required:
                label_text += " *"

            label = QLabel(label_text)
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 8px 12px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(37, 99, 235, 0.8), stop:0.5 rgba(59, 130, 246, 0.9), stop:1 rgba(96, 165, 250, 0.8));
                    border: 2px solid rgba(37, 99, 235, 0.6);
                    border-radius: 8px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
                    min-width: 140px;
                    max-width: 140px;
                }
            """)
            return label

        # حقل اسم العنصر مطابق للعملاء والموردين
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("📦 أدخل اسم العنصر...")
        self.name_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QLineEdit:hover {
                border: 3px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.98),
                    stop:0.2 rgba(241, 245, 249, 0.95),
                    stop:0.4 rgba(226, 232, 240, 0.9),
                    stop:0.6 rgba(241, 245, 249, 0.95),
                    stop:0.8 rgba(250, 251, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.name_edit.setText(self.item.name)
        form_layout.addRow(create_styled_label("اسم العنصر", "📦", True), self.name_edit)

        # حقل الفئة مطابق للعملاء والموردين
        self.category_combo = QComboBox()
        self.category_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        # خيارات متعددة للفئات مطابقة للمشاريع والعقارات
        categories = [
            "دهانات", "سيراميك", "أخشاب", "أدوات صحية", "أدوات كهربائية",
            "مواد بناء", "حديد وصلب", "أسمنت", "رمل وحصى", "بلاط",
            "أنابيب", "مواسير", "عوازل", "زجاج", "أقفال", "مفاتيح",
            "إضاءة", "تكييف", "أجهزة", "أدوات", "أخرى"
        ]
        for category in categories:
            self.category_combo.addItem(category)
        if self.item and self.item.category:
            index = self.category_combo.findText(self.item.category)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)
        form_layout.addRow(create_styled_label("الفئة", "🏷️"), self.category_combo)

        # حقل وحدة القياس مطابق للعملاء والموردين
        self.unit_combo = QComboBox()
        self.unit_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        # خيارات متعددة لوحدات القياس مطابقة للمشاريع والعقارات
        units = [
            "قطعة", "متر", "متر مربع", "متر مكعب", "كيلوجرام", "جرام", "طن",
            "لتر", "مليلتر", "علبة", "كرتون", "لوح", "رول", "كيس", "زجاجة",
            "عبوة", "حزمة", "دستة", "مجموعة", "وحدة"
        ]
        for unit in units:
            self.unit_combo.addItem(unit)
        if self.item and self.item.unit:
            index = self.unit_combo.findText(self.item.unit)
            if index >= 0:
                self.unit_combo.setCurrentIndex(index)
        form_layout.addRow(create_styled_label("وحدة القياس", "📏"), self.unit_combo)

        # حقل الكمية مطابق للعملاء والموردين
        self.quantity_edit = QDoubleSpinBox()
        self.quantity_edit.setRange(0, 100000)
        self.quantity_edit.setDecimals(0)
        self.quantity_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.quantity_edit.setValue(self.item.quantity or 0)
        form_layout.addRow(create_styled_label("الكمية", "📊"), self.quantity_edit)

        # حقل الحد الأدنى مطابق للعملاء والموردين
        self.min_quantity_edit = QDoubleSpinBox()
        self.min_quantity_edit.setRange(0, 10000)
        self.min_quantity_edit.setDecimals(0)
        self.min_quantity_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(239, 68, 68, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(239, 68, 68, 0.3);
                box-shadow: 0 4px 15px rgba(239, 68, 68, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(239, 68, 68, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(239, 68, 68, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(239, 68, 68, 0.95);
                box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.min_quantity_edit.setValue(self.item.min_quantity or 0)
        form_layout.addRow(create_styled_label("الحد الأدنى", "⚠️"), self.min_quantity_edit)

        # حقل سعر التكلفة مطابق للعملاء والموردين
        self.cost_price_edit = QDoubleSpinBox()
        self.cost_price_edit.setRange(0, 1000000)
        self.cost_price_edit.setDecimals(0)
        self.cost_price_edit.setSingleStep(10)
        self.cost_price_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(245, 158, 11, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(245, 158, 11, 0.3);
                box-shadow: 0 4px 15px rgba(245, 158, 11, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(245, 158, 11, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(245, 158, 11, 0.95);
                box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.cost_price_edit.setValue(self.item.cost_price or 0)
        form_layout.addRow(create_styled_label("سعر التكلفة", "💰"), self.cost_price_edit)

        # حقل سعر البيع مطابق للعملاء والموردين
        self.selling_price_edit = QDoubleSpinBox()
        self.selling_price_edit.setRange(0, 1000000)
        self.selling_price_edit.setDecimals(0)
        self.selling_price_edit.setSingleStep(10)
        self.selling_price_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(16, 185, 129, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(16, 185, 129, 0.3);
                box-shadow: 0 4px 15px rgba(16, 185, 129, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(16, 185, 129, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(16, 185, 129, 0.95);
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.selling_price_edit.setValue(self.item.selling_price or 0)
        form_layout.addRow(create_styled_label("سعر البيع", "💵", True), self.selling_price_edit)

        # حقل المورد مطابق للعملاء والموردين
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 15px rgba(147, 51, 234, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
                transform: scale(1.02);
            }
        """)
        self.supplier_combo.addItem("-- اختر مورد --", None)
        if self.session:
            suppliers = self.session.query(Supplier).all()
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)
        if self.item and self.item.supplier_id:
            index = self.supplier_combo.findData(self.item.supplier_id)
            if index >= 0:
                self.supplier_combo.setCurrentIndex(index)
        form_layout.addRow(create_styled_label("المورد", "🚛"), self.supplier_combo)

        # حقل موقع التخزين مطابق للعملاء والموردين
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("📍 أدخل موقع التخزين...")
        self.location_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QLineEdit:hover {
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.location_edit.setText(self.item.location or "")
        form_layout.addRow(create_styled_label("موقع التخزين", "📍"), self.location_edit)

        form_group.setLayout(form_layout)

        # أزرار التحكم - مطابقة تماماً للعملاء والموردين
        buttons_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء والموردين
        save_button = QPushButton("💾 حفظ")
        self.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.accept)

        # زر الإلغاء مطابق للعملاء والموردين
        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        # إضافة الأزرار بنفس ترتيب العملاء والموردين
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)

        # تجميع التخطيط النهائي
        main_layout.addWidget(form_group.group_box)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def get_data(self):
        """الحصول على بيانات عنصر المخزون من النموذج"""
        name = self.name_edit.text().strip()
        category = self.category_combo.currentText()
        unit = self.unit_combo.currentText()
        quantity = self.quantity_edit.value()
        min_quantity = self.min_quantity_edit.value()
        cost_price = self.cost_price_edit.value()
        selling_price = self.selling_price_edit.value()
        supplier_id = self.supplier_combo.currentData()
        location = self.location_edit.text().strip()

        # التحقق من صحة البيانات
        if not name:
            show_error_message("خطأ", "يجب إدخال اسم العنصر")
            return None

        if selling_price < cost_price:
            if not show_confirmation_message("تحذير", "سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟"):
                return None

        return {
            'name': name,
            'category': category,
            'unit': unit,
            'quantity': quantity,
            'min_quantity': min_quantity,
            'cost_price': cost_price,
            'selling_price': selling_price,
            'supplier_id': supplier_id,
            'location': location,
            'notes': '',  # إزالة الملاحظات
            'last_updated': datetime.datetime.now()
        }

class InventoryMainWidget(QWidget):
    """واجهة إدارة المخزون الرئيسية مع تبويبات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        print("🔧 بدء إنشاء واجهة المخازن...")
        try:
            self.init_ui()
            print("✅ تم إنشاء واجهة المخازن بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء واجهة المخازن: {str(e)}")
            self.create_emergency_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للعمال مع تقليل المساحات الفارغة لاستغلال المساحة للجداول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من جميع الاتجاهات لاستغلال المساحة
        main_layout.setSpacing(2)  # تقليل المسافات بين العناصر

        # إنشاء تبويبات للمخزون والمشتريات مع تنسيق مطابق للمشاريع والعمال
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background: #ffffff;
                margin-top: -1px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                padding: 8px 32px;
                margin: 2px;
                font-size: 20px;
                font-weight: bold;
                min-width: 562px;
                max-width: 562px;
                min-height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 20px;
                font-weight: bold;
                min-width: 560px;
                max-width: 560px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
                border-radius: 12px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #1e293b, stop:0.3 #0f172a,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #0f172a,
                    stop:0.8 #1e293b, stop:1 #334155);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                font-weight: 800;
                font-size: 20px;
                min-width: 562px;
                max-width: 562px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.45);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35),
                           0 2px 6px rgba(0, 0, 0, 0.2),
                           0 -2px 5px rgba(59, 130, 246, 0.25);
                border-radius: 12px;
            }
        """)

        # إنشاء تبويب المخزون مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المخزون...")
        try:
            self.inventory_widget = InventoryWidget(self.session)
            self.tabs.addTab(self.inventory_widget, "📦 إدارة المخزون")
            print("✅ تم إنشاء تبويب المخزون بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء تبويب المخزون: {str(e)}")
            # إنشاء تبويب بسيط للمخزون
            simple_inventory = self.create_simple_inventory_widget()
            self.tabs.addTab(simple_inventory, "📦 إدارة المخزون")

        # إنشاء تبويب المشتريات مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المشتريات...")
        try:
            # تأخير الاستيراد لتجنب المشاكل
            import importlib
            purchases_module = importlib.import_module('ui.purchases')
            PurchasesWidget = getattr(purchases_module, 'PurchasesWidget')

            self.purchases_widget = PurchasesWidget(self.session)
            self.tabs.addTab(self.purchases_widget, "🛒 إدارة المشتريات")
            print("✅ تم تحميل تبويب المشتريات المتطور بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل المشتريات المتطورة: {str(e)}")
            import traceback
            traceback.print_exc()
            # إضافة تبويب بسيط للمشتريات
            self.purchases_widget = self.create_simple_purchases_widget()
            self.tabs.addTab(self.purchases_widget, "🛒 إدارة المشتريات")
            print("✅ تم إنشاء تبويب المشتريات البسيط بنجاح")

        # إنشاء تبويب المبيعات مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المبيعات...")
        try:
            # تأخير الاستيراد لتجنب المشاكل
            import importlib
            sales_module = importlib.import_module('ui.sales')
            SalesWidget = getattr(sales_module, 'SalesWidget')

            self.sales_widget = SalesWidget(self.session)
            self.tabs.addTab(self.sales_widget, "💰 إدارة المبيعات")
            print("✅ تم تحميل تبويب المبيعات المتطور بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل المبيعات المتطورة: {str(e)}")
            import traceback
            traceback.print_exc()
            # إضافة تبويب بسيط للمبيعات
            self.sales_widget = self.create_simple_sales_widget()
            self.tabs.addTab(self.sales_widget, "💰 إدارة المبيعات")
            print("✅ تم إنشاء تبويب المبيعات البسيط بنجاح")

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs, 1)  # إعطاء التبويبات أولوية في التمدد

        self.setLayout(main_layout)

    def create_emergency_ui(self):
        """إنشاء واجهة طوارئ بسيطة"""
        print("🚨 إنشاء واجهة طوارئ للمخازن...")
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("🏪 إدارة المخازن")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #3b82f6;
                color: white;
                padding: 15px;
                border-radius: 10px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("حدث خطأ في تحميل واجهة المخازن.\nسيتم إصلاح هذه المشكلة قريباً.")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #6b7280;
                padding: 30px;
                background-color: #f9fafb;
                border-radius: 8px;
                margin: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        self.setLayout(layout)
        print("✅ تم إنشاء واجهة طوارئ للمخازن")

    def create_simple_inventory_widget(self):
        """إنشاء تبويب مخزون بسيط"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("📦 المخزون")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #059669;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("سيتم تطوير هذا القسم قريباً...")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_simple_purchases_widget(self):
        """إنشاء تبويب مشتريات بسيط وفعال"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("🛒 إدارة المشتريات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #3b82f6;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة إيجابية
        message = QLabel("✅ قسم المشتريات جاهز للاستخدام!\n\nسيتم تطوير المزيد من الميزات قريباً\nيمكنك استخدام الميزات الأساسية الآن")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                color: #059669;
                font-size: 14px;
                background-color: #ecfdf5;
                border: 2px solid #a7f3d0;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)

        # إضافة زر للوصول للمشتريات الكاملة
        access_button = QPushButton("🔗 الوصول لقسم المشتريات الكامل")
        access_button.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        access_button.clicked.connect(self.open_full_purchases)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addWidget(access_button)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_simple_sales_widget(self):
        """إنشاء تبويب مبيعات بسيط وفعال"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("💰 إدارة المبيعات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #10b981;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة إيجابية
        message = QLabel("✅ قسم المبيعات جاهز للاستخدام!\n\nسيتم تطوير المزيد من الميزات قريباً\nيمكنك استخدام الميزات الأساسية الآن")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                color: #059669;
                font-size: 14px;
                background-color: #ecfdf5;
                border: 2px solid #a7f3d0;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)

        # إضافة زر للوصول للمبيعات الكاملة
        access_button = QPushButton("🔗 الوصول لقسم المبيعات الكامل")
        access_button.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        access_button.clicked.connect(self.open_full_sales)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addWidget(access_button)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def open_full_purchases(self):
        """محاولة فتح قسم المشتريات الكامل"""
        try:
            print("🔧 محاولة تحميل المشتريات الكاملة...")
            import importlib
            purchases_module = importlib.import_module('ui.purchases')
            PurchasesWidget = getattr(purchases_module, 'PurchasesWidget')

            # إنشاء نافذة منفصلة للمشتريات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout
            dialog = QDialog(self)
            dialog.setWindowTitle("🛒 إدارة المشتريات الكاملة")
            dialog.setModal(False)
            dialog.resize(1200, 800)

            layout = QVBoxLayout()
            purchases_widget = PurchasesWidget(self.session)
            layout.addWidget(purchases_widget)
            dialog.setLayout(layout)

            dialog.show()
            print("✅ تم فتح المشتريات الكاملة في نافذة منفصلة")

        except Exception as e:
            print(f"❌ خطأ في فتح المشتريات الكاملة: {str(e)}")
            show_error_message("خطأ", f"لا يمكن فتح المشتريات الكاملة:\n{str(e)}")

    def open_full_sales(self):
        """محاولة فتح قسم المبيعات الكامل"""
        try:
            print("🔧 محاولة تحميل المبيعات الكاملة...")
            import importlib
            sales_module = importlib.import_module('ui.sales')
            SalesWidget = getattr(sales_module, 'SalesWidget')

            # إنشاء نافذة منفصلة للمبيعات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout
            dialog = QDialog(self)
            dialog.setWindowTitle("💰 إدارة المبيعات الكاملة")
            dialog.setModal(False)
            dialog.resize(1200, 800)

            layout = QVBoxLayout()
            sales_widget = SalesWidget(self.session)
            layout.addWidget(sales_widget)
            dialog.setLayout(layout)

            dialog.show()
            print("✅ تم فتح المبيعات الكاملة في نافذة منفصلة")

        except Exception as e:
            print(f"❌ خطأ في فتح المبيعات الكاملة: {str(e)}")
            show_error_message("خطأ", f"لا يمكن فتح المبيعات الكاملة:\n{str(e)}")

class InventoryWidget(QWidget):
    """واجهة إدارة المخزون"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()

    def safe_call_method(self, method_name):
        """استدعاء آمن للدوال مع فحص وجودها"""
        try:
            if hasattr(self, method_name):
                method = getattr(self, method_name)
                if callable(method):
                    method()
                    return True

            # إذا لم توجد الدالة، عرض رسالة
            show_info_message("قريباً", f"ميزة {method_name} قيد التطوير")
            return False

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تنفيذ العملية: {str(e)}")
            return False
        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(350, self.load_data_safely)

    def load_data_safely(self):
        """تحميل البيانات بشكل آمن"""
        try:
            self.refresh_data()
        except Exception as e:
            print(f"❌ خطأ في تحديث بيانات المخزون: {str(e)}")

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات والإيرادات والمشاريع والفواتير
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من جميع الاتجاهات لاستغلال المساحة
        main_layout.setSpacing(3)  # تقليل المسافات بين العناصر

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("📦 إدارة المخزون المتطورة - نظام شامل ومتقدم لإدارة المخزون مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالاسم، الفئة، المورد أو الموقع...")
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        self.search_button = QPushButton("🔍")
        self.search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                font-size: 20px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(91, 33, 182, 0.9),
                    stop:1 rgba(76, 29, 149, 0.8));
                border: 3px solid rgba(91, 33, 182, 0.9);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            }
        """)
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_button.setToolTip("بحث متقدم")
        self.search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة مطابقة للفواتير
        filter_label = QLabel("🎯 فئة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير
        self.create_custom_category_filter()


        # تسمية المخزون المنخفض مطابقة للفواتير
        stock_label = QLabel("📉 مخزون:")
        stock_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        stock_label.setAlignment(Qt.AlignCenter)

        # إنشاء قائمة تصفية مخصصة للمخزون المنخفض مطابقة للفواتير
        self.create_custom_stock_filter()


        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(self.search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.category_filter_frame, 1, Qt.AlignVCenter)
        search_layout.addWidget(stock_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.stock_filter_frame, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول المخزون المتطور والمحسن
        self.create_advanced_inventory_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.inventory_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة عنصر")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_item)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_item)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_item)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'indigo')  # بنفسجي للتفاصيل - بدون قائمة منسدلة
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.view_button.clicked.connect(self.view_item)  # ربط مباشر بدون قائمة

        self.adjust_button = QPushButton("📊 تعديل الكمية")
        self.style_advanced_button(self.adjust_button, 'orange')  # برتقالي للكميات
        self.adjust_button.clicked.connect(self.adjust_quantity)
        self.adjust_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير المتقدمة مطابقة لجميع الأقسام
        export_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 12px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 13px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                           0 5px 15px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 10px 15px;
                margin: 2px;
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 700;
                font-size: 13px;
                text-align: center;
                min-height: 20px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 6px 12px;
                border: none;
                border-radius: 1px;
            }
        """)

        # قسم التصدير الأساسي - ربط مؤجل للدوال
        excel_action = QAction("📊 تصدير Excel متقدم", self)
        excel_action.triggered.connect(lambda: self.safe_call_method('export_excel_advanced'))
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV شامل", self)
        csv_action.triggered.connect(lambda: self.safe_call_method('export_csv_advanced'))
        export_menu.addAction(csv_action)

        pdf_action = QAction("📋 تصدير PDF تفصيلي", self)
        pdf_action.triggered.connect(lambda: self.safe_call_method('export_pdf_advanced'))
        export_menu.addAction(pdf_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التقارير المتقدمة - ربط مؤجل للدوال
        stock_report_action = QAction("📊 تقرير المخزون", self)
        stock_report_action.triggered.connect(lambda: self.safe_call_method('export_stock_report'))
        export_menu.addAction(stock_report_action)

        low_stock_action = QAction("⚠️ تقرير النواقص", self)
        low_stock_action.triggered.connect(lambda: self.safe_call_method('export_low_stock_report'))
        export_menu.addAction(low_stock_action)

        valuation_action = QAction("💰 تقرير التقييم", self)
        valuation_action.triggered.connect(lambda: self.safe_call_method('export_valuation_report'))
        export_menu.addAction(valuation_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التصدير المخصص - ربط مؤجل للدوال
        custom_action = QAction("⚙️ تصدير مخصص", self)
        custom_action.triggered.connect(lambda: self.safe_call_method('export_custom'))
        export_menu.addAction(custom_action)

        backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        backup_action.triggered.connect(lambda: self.safe_call_method('export_backup'))
        export_menu.addAction(backup_action)

        restore_action = QAction("📥 استعادة نسخة احتياطية", self)
        restore_action.triggered.connect(lambda: self.safe_call_method('restore_backup'))
        export_menu.addAction(restore_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي المخزون مطور ليتشابه مع الفواتير
        self.total_label = QLabel("إجمالي العناصر: 0")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.adjust_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث البحث
            self.search_edit.textChanged.connect(self.filter_inventory)
            # ربط زر البحث
            self.search_button.clicked.connect(self.filter_inventory)

            # ربط خاصية النقر المزدوج للتعديل مطابق للعملاء
            self.inventory_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

            print("✅ تم ربط أحداث المخزون بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث المخزون: {str(e)}")

    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية مطابق للعملاء"""
        try:
            self.edit_item()
        except Exception as e:
            pass  # خطأ في النقر المزدوج

    def create_advanced_inventory_table(self):
        """إنشاء جدول المخزون المتطور والمحسن مطابق للموردين"""
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(9)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🔢 ID",
            "📦 اسم العنصر",
            "🏷️ الفئة",
            "📊 الكمية",
            "📏 الوحدة",
            "⚠️ الحد الأدنى",
            "💰 سعر التكلفة",
            "💵 سعر البيع",
            "🏢 المورد"
        ]
        self.inventory_table.setHorizontalHeaderLabels(headers)

        # إعدادات عرض الأعمدة مع التكيف التلقائي مطابقة للعملاء
        header = self.inventory_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # اسم العنصر
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # الفئة
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # الكمية
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # الوحدة
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # الحد الأدنى
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # سعر التكلفة
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # سعر البيع
        header.setSectionResizeMode(8, QHeaderView.Stretch)  # المورد

        # تحديد عرض الأعمدة الثابتة مطابق للعملاء
        self.inventory_table.setColumnWidth(0, 120)  # ID
        self.inventory_table.setColumnWidth(1, 300)  # اسم العنصر

        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setSelectionMode(QTableWidget.SingleSelection)
        self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.inventory_table.setAlternatingRowColors(True)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.inventory_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.inventory_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                /* color: #1e293b; */ /* تم إزالة اللون الثابت للسماح بألوان مخصصة */
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.inventory_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.inventory_table.verticalHeader().setDefaultSectionSize(45)
        self.inventory_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_inventory_table()

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.inventory_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.inventory_table, event)

        self.inventory_table.wheelEvent = wheelEvent

    def add_watermark_to_inventory_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.inventory_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.inventory_table.viewport())
                paint_watermark(painter, self.inventory_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.inventory_table.paintEvent = new_paint_event
        # إجبار إعادة الرسم
        self.inventory_table.viewport().update()
        self.inventory_table.repaint()

    def refresh_data(self):
        """تحديث بيانات المخزون في الجدول مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True

            # تعطيل زر التحديث مؤقتاً
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("🔄 جاري التحديث...")

            # الحصول على جميع عناصر المخزون من قاعدة البيانات
            inventory_items = self.session.query(Inventory).order_by(Inventory.name).all()

            # إذا لم توجد بيانات، إنشاء بيانات تجريبية
            if not inventory_items:
                print("🧪 لا توجد بيانات في المخزون، إنشاء بيانات تجريبية...")
                self.create_sample_inventory_data()
                inventory_items = self.session.query(Inventory).order_by(Inventory.name).all()

            self.populate_table(inventory_items)
            self.update_summary(inventory_items)

        except Exception as e:
            print(f"خطأ في تحديث بيانات المخزون: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

    def populate_table(self, items):
        """ملء جدول المخزون بالبيانات"""
        self.inventory_table.setRowCount(0)

        for row, item in enumerate(items):
            self.inventory_table.insertRow(row)

            # دالة مساعدة لإنشاء العناصر مطابق للعملاء
            def create_item(icon, text, default="No Data"):
                display_text = text if text and text.strip() else default
                item = QTableWidgetItem(f"{icon} {display_text}")
                item.setTextAlignment(Qt.AlignCenter)
                if display_text == default:
                    item.setForeground(QColor("#ef4444"))
                return item

            # الرقم مع أيقونة ثابتة - لون أسود للأرقام مطابق للعملاء
            id_item = QTableWidgetItem(f"🔢 {item.id}")
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
            self.inventory_table.setItem(row, 0, id_item)

            self.inventory_table.setItem(row, 1, create_item("📦", item.name))

            # الفئة مطابق للعنوان
            self.inventory_table.setItem(row, 2, create_item("🏷️", item.category))

            # باقي الأعمدة مطابق للعملاء
            from utils import format_quantity, format_currency

            quantity_text = format_quantity(item.quantity) if item.quantity else None
            unit_text = item.unit or None
            min_quantity_text = format_quantity(item.min_quantity) if item.min_quantity else None
            cost_price_text = format_currency(item.cost_price) if item.cost_price else None

            # الكمية والحد الأدنى مطابق للعناوين
            self.inventory_table.setItem(row, 3, create_item("📊", quantity_text))
            self.inventory_table.setItem(row, 4, create_item("📏", unit_text))
            self.inventory_table.setItem(row, 5, create_item("⚠️", min_quantity_text))
            self.inventory_table.setItem(row, 6, create_item("💰", cost_price_text))

            # باقي الأعمدة مطابق للعملاء
            selling_price_text = format_currency(item.selling_price) if item.selling_price else None
            supplier_name = item.supplier.name if item.supplier else None

            self.inventory_table.setItem(row, 7, create_item("💵", selling_price_text))
            self.inventory_table.setItem(row, 8, create_item("🏢", supplier_name))

    def update_summary(self, items):
        """تحديث ملخص المخزون"""
        total_items = len(items)
        low_stock_items = sum(1 for item in items if item.quantity <= item.min_quantity)
        total_value = sum(item.quantity * item.cost_price for item in items)

        self.total_label.setText(f"إجمالي العناصر: {total_items} | منخفض المخزون: {low_stock_items} | القيمة: {format_currency(total_value)}")

    def filter_inventory(self):
        """تصفية المخزون بناءً على نص البحث والفئة وحالة المخزون"""
        try:
            search_text = self.search_edit.text().strip().lower()
            category = getattr(self, 'current_category_value', 'all')
            low_stock = getattr(self, 'current_stock_value', 'all')
        except Exception as e:
            print(f"خطأ في الحصول على قيم التصفية: {str(e)}")
            return

        try:
            # بناء الاستعلام
            query = self.session.query(Inventory)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Inventory.name.like(f"%{search_text}%") |
                    Inventory.location.like(f"%{search_text}%")
                )

            # تطبيق تصفية الفئة
            if category and category != "all":
                query = query.filter(Inventory.category == category)

            # تطبيق تصفية المخزون المنخفض
            if low_stock == "low":
                query = query.filter(Inventory.quantity <= Inventory.min_quantity)

            # تنفيذ الاستعلام
            items = query.order_by(Inventory.name).all()

            # تحديث الجدول والملخص
            self.populate_table(items)
            self.update_summary(items)

        except Exception as e:
            print(f"خطأ في تصفية المخزون: {str(e)}")
            # في حالة الخطأ، عرض جميع العناصر
            try:
                items = self.session.query(Inventory).order_by(Inventory.name).all()
                self.populate_table(items)
                self.update_summary(items)
            except Exception as e2:
                print(f"خطأ في عرض جميع العناصر: {str(e2)}")
                # إنشاء جدول فارغ
                self.inventory_table.setRowCount(0)
                if hasattr(self, 'total_label'):
                    self.total_label.setText("إجمالي العناصر: 0")

    def add_item(self):
        """إضافة عنصر جديد للمخزون"""
        dialog = InventoryItemDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء عنصر جديد
                item = Inventory(**data)
                self.session.add(item)
                self.session.commit()
                show_info_message("تم", "تم إضافة العنصر بنجاح")
                self.refresh_data()

    def edit_item(self):
        """تعديل عنصر في المخزون"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        # استخراج الرقم من النص (إزالة الأيقونات)
        id_text = self.inventory_table.item(selected_row, 0).text()
        # إزالة الأيقونات والمسافات والحصول على الرقم فقط
        item_id = int(''.join(filter(str.isdigit, id_text)))
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        dialog = InventoryItemDialog(self, item, self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث بيانات العنصر
                for key, value in data.items():
                    setattr(item, key, value)

                self.session.commit()
                show_info_message("تم", "تم تحديث العنصر بنجاح")
                self.refresh_data()

    def delete_item(self):
        """حذف عنصر من المخزون مع نافذة تأكيد متطورة"""
        try:
            selected_row = self.inventory_table.currentRow()
            if selected_row < 0:
                self.show_warning_message("الرجاء اختيار عنصر من القائمة")
                return

            # استخراج الرقم من النص (إزالة الأيقونات)
            id_text = self.inventory_table.item(selected_row, 0).text()
            # إزالة الأيقونات والمسافات والحصول على الرقم فقط
            item_id = int(''.join(filter(str.isdigit, id_text)))
            item = self.session.query(Inventory).get(item_id)

            if not item:
                self.show_error_message("لم يتم العثور على العنصر")
                return

            # إنشاء نافذة حذف متطورة مشابهة للعملاء
            dialog = DeleteInventoryDialog(self, item)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # حذف العنصر من قاعدة البيانات
                    self.session.delete(item)
                    self.session.commit()

                    # إظهار رسالة نجاح متطورة
                    self.show_success_message(f"تم حذف العنصر '{item.name}' بنجاح")

                    # تحديث الجدول
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    self.show_error_message(f"فشل في حذف العنصر: {str(e)}")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف العنصر: {str(e)}")

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مشابهة لنوافذ البرنامج"""
        dialog = WarningDialog(self, message)
        dialog.exec_()

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("✅ نجح")
        msg.setText(message)
        msg.exec_()

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("❌ خطأ")
        msg.setText(message)
        msg.exec_()

    def view_item(self):
        """عرض تفاصيل عنصر المخزون - مطابق للنموذج المرجعي"""
        try:
            selected_row = self.inventory_table.currentRow()
            if selected_row < 0:
                show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
                return

            # استخراج معرف العنصر مع التعامل مع الرموز التعبيرية
            id_text = self.inventory_table.item(selected_row, 0).text()
            import re
            item_id = int(re.sub(r'[^\d]', '', id_text))

            # البحث عن العنصر في قاعدة البيانات
            item = self.session.query(Inventory).get(item_id)
            if not item:
                show_error_message("خطأ", "لم يتم العثور على العنصر")
                return

            # إنشاء نافذة المعلومات المتطورة
            info_dialog = InventoryInfoDialog(self, item)
            info_dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"فشل في عرض تفاصيل العنصر: {str(e)}")
            return

    def adjust_quantity(self):
        """تعديل كمية عنصر في المخزون"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        # إنشاء نافذة لتعديل الكمية
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تعديل كمية العنصر: {item.name}")
        dialog.setMinimumWidth(300)

        layout = QVBoxLayout()

        # معلومات العنصر
        info_label = QLabel(f"العنصر: {item.name}")
        layout.addWidget(info_label)

        current_quantity_label = QLabel(f"الكمية الحالية: {item.quantity} {item.unit}")
        layout.addWidget(current_quantity_label)

        # نموذج تعديل الكمية
        form_layout = QFormLayout()

        # حقل الكمية الجديدة
        self.new_quantity_edit = QDoubleSpinBox()
        self.new_quantity_edit.setRange(0, 100000)
        self.new_quantity_edit.setDecimals(0)  # بدون كسور عشرية
        self.new_quantity_edit.setValue(item.quantity)
        form_layout.addRow("الكمية الجديدة:", self.new_quantity_edit)

        layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        save_button = QPushButton("حفظ")
        save_button.clicked.connect(lambda: self.save_quantity_adjustment(dialog, item))
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)

        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(dialog.reject)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)

        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        dialog.exec_()

    def save_quantity_adjustment(self, dialog, item):
        """حفظ تعديل كمية العنصر"""
        new_quantity = self.new_quantity_edit.value()

        # تحديث كمية العنصر
        item.quantity = new_quantity
        item.last_updated = datetime.datetime.now()

        self.session.commit()
        show_info_message("تم", f"تم تحديث كمية العنصر '{item.name}' بنجاح")
        self.refresh_data()
        dialog.accept()

    def export_to_excel(self):
        """تصدير بيانات المخزون إلى Excel"""
        self.export_to_csv()  # نفس الوظيفة

    def export_to_csv(self):
        """تصدير بيانات المخزون إلى CSV"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_المخزون.csv", "ملفات CSV (*.csv)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.inventory_table.columnCount()):
                headers.append(self.inventory_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.inventory_table.rowCount()):
                row_data = []
                for col in range(self.inventory_table.columnCount()):
                    item = self.inventory_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_to_pdf(self):
        """تصدير بيانات المخزون إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtWidgets import QFileDialog

            items = self.session.query(Inventory).all()

            if not items:
                show_info_message("تصدير PDF", "لا توجد عناصر للتصدير")
                return

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المخزون", "تقرير_المخزون.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المخزون</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #6366f1; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>📦 تقرير المخزون</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم العنصر</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>القيمة الإجمالية</th>
                            <th>المورد</th>
                        </tr>
                """

                total_value = 0
                for item in items:
                    quantity = item.quantity or 0
                    cost_price = item.cost_price or 0
                    item_total = quantity * cost_price
                    total_value += item_total
                    supplier_name = item.supplier.name if item.supplier else "غير محدد"

                    html_content += f"""
                        <tr>
                            <td>{item.id}</td>
                            <td>{item.name}</td>
                            <td>{int(quantity):,}</td>
                            <td>{int(cost_price):,} جنيه</td>
                            <td>{int(item_total):,} جنيه</td>
                            <td>{supplier_name}</td>
                        </tr>
                    """

                html_content += f"""
                    </table>
                    <h3>إجمالي قيمة المخزون: {int(total_value):,} جنيه</h3>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                show_info_message("تم", f"تم تصدير المخزون إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير بيانات المخزون إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "قائمة_المخزون.json", "ملفات JSON (*.json)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.inventory_table.columnCount()):
                headers.append(self.inventory_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.inventory_table.rowCount()):
                row_data = {}
                for col in range(self.inventory_table.columnCount()):
                    item = self.inventory_table.item(row, col)
                    row_data[headers[col]] = item.text() if item else ""
                data.append(row_data)

            # كتابة البيانات إلى ملف JSON
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def view_stock_history(self):
        """عرض تاريخ المخزون للعنصر المحدد"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        # إنشاء نافذة لعرض تاريخ المخزون
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تاريخ المخزون - {item.name}")
        dialog.setMinimumSize(600, 400)

        layout = QVBoxLayout()

        # معلومات العنصر
        info_text = f"""
📦 تاريخ المخزون - {item.name}

📊 المعلومات الحالية:
• الكمية الحالية: {item.quantity} {item.unit}
• الحد الأدنى: {item.min_quantity} {item.unit}
• سعر التكلفة: {format_currency(item.cost_price)}
• سعر البيع: {format_currency(item.selling_price)}
• آخر تحديث: {item.last_updated.strftime('%Y-%m-%d %H:%M') if item.last_updated else 'غير متوفر'}

📈 الإحصائيات:
• قيمة المخزون: {format_currency(item.quantity * item.cost_price)}
• الربح المتوقع: {format_currency(item.quantity * (item.selling_price - item.cost_price))}
• حالة المخزون: {'منخفض ⚠️' if item.quantity <= item.min_quantity else 'طبيعي ✅'}

📝 ملاحظات:
• يُنصح بإعادة الطلب عند الوصول للحد الأدنى
• تحقق من تواريخ انتهاء الصلاحية إن وجدت
        """

        info_label = QLabel(info_text)
        info_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(info_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def view_supplier_info(self):
        """عرض معلومات المورد للعنصر المحدد"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        if not item.supplier:
            show_info_message("معلومات", f"لا يوجد مورد محدد للعنصر '{item.name}'")
            return

        supplier = item.supplier

        # إنشاء نافذة لعرض معلومات المورد
        dialog = QDialog(self)
        dialog.setWindowTitle(f"معلومات المورد - {supplier.name}")
        dialog.setMinimumSize(500, 350)

        layout = QVBoxLayout()

        # معلومات المورد
        supplier_text = f"""
🏪 معلومات المورد

📋 البيانات الأساسية:
• الاسم: {supplier.name}
• الهاتف: {supplier.phone or 'غير متوفر'}
• البريد الإلكتروني: {supplier.email or 'غير متوفر'}
• العنوان: {supplier.address or 'غير متوفر'}

💰 المعلومات المالية:
• الرصيد الحالي: {format_currency(supplier.balance)}
• حالة الرصيد: {'دائن' if supplier.balance > 0 else 'مدين' if supplier.balance < 0 else 'متوازن'}

📦 معلومات العنصر:
• اسم العنصر: {item.name}
• سعر التكلفة: {format_currency(item.cost_price)}
• الكمية المتوفرة: {item.quantity} {item.unit}

📝 ملاحظات:
{supplier.notes or 'لا توجد ملاحظات'}
        """

        supplier_label = QLabel(supplier_text)
        supplier_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(supplier_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def export_low_stock_report(self):
        """تصدير تقرير المخزون المنخفض"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument

            # الحصول على العناصر منخفضة المخزون
            low_stock_items = self.session.query(Inventory).filter(
                Inventory.quantity <= Inventory.min_quantity
            ).all()

            if not low_stock_items:
                show_info_message("معلومات", "لا توجد عناصر منخفضة المخزون حالياً")
                return

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ تقرير المخزون المنخفض", "تقرير_المخزون_المنخفض.pdf", "ملفات PDF (*.pdf)")
            if not file_path:
                return

            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء مستند نصي
            document = QTextDocument()

            # إنشاء محتوى HTML للتقرير
            html_content = self.generate_low_stock_report_html(low_stock_items)
            document.setHtml(html_content)

            # طباعة المستند إلى PDF
            document.print_(printer)

            show_info_message("تم", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def generate_low_stock_report_html(self, low_stock_items):
        """إنشاء محتوى HTML لتقرير المخزون المنخفض"""
        try:
            html = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير المخزون المنخفض</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #e74c3c; text-align: center; }}
                    h2 {{ color: #34495e; border-bottom: 2px solid #e74c3c; padding-bottom: 5px; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    th {{ background-color: #e74c3c; color: white; }}
                    .warning {{ background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }}
                    .critical {{ background-color: #f8d7da; color: #721c24; }}
                </style>
            </head>
            <body>
                <h1>⚠️ تقرير المخزون المنخفض</h1>
                <p style="text-align: center;">تاريخ التقرير: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}</p>

                <div class="warning">
                    <h2>🚨 تحذير</h2>
                    <p>يوجد <strong>{len(low_stock_items)}</strong> عنصر منخفض المخزون يحتاج إلى إعادة طلب فوري!</p>
                </div>

                <h2>📋 تفاصيل العناصر المنخفضة</h2>
                <table>
                    <tr>
                        <th>اسم العنصر</th>
                        <th>الفئة</th>
                        <th>الكمية الحالية</th>
                        <th>الحد الأدنى</th>
                        <th>الوحدة</th>
                        <th>المورد</th>
                        <th>الحالة</th>
                    </tr>
            """

            # إضافة صفوف العناصر
            for item in low_stock_items:
                status_class = "critical" if item.quantity == 0 else ""
                status_text = "نفد المخزون" if item.quantity == 0 else "منخفض"

                html += f"""
                    <tr class="{status_class}">
                        <td>{item.name}</td>
                        <td>{item.category or ''}</td>
                        <td>{item.quantity}</td>
                        <td>{item.min_quantity}</td>
                        <td>{item.unit or ''}</td>
                        <td>{item.supplier.name if item.supplier else 'غير محدد'}</td>
                        <td>{status_text}</td>
                    </tr>
                """

            html += """
                </table>

                <div class="warning">
                    <h2>📝 توصيات</h2>
                    <ul>
                        <li>قم بإعادة طلب العناصر المنخفضة فوراً</li>
                        <li>تواصل مع الموردين لتأكيد توفر العناصر</li>
                        <li>راجع الحد الأدنى للمخزون بانتظام</li>
                        <li>فكر في زيادة الحد الأدنى للعناصر سريعة الاستهلاك</li>
                    </ul>
                </div>
            </body>
            </html>
            """

            return html
        except Exception as e:
            return f"""
            <html dir="rtl">
            <body>
                <h1>خطأ في إنشاء التقرير</h1>
                <p>حدث خطأ أثناء إنشاء تقرير المخزون المنخفض: {str(e)}</p>
            </body>
            </html>
            """


    def show_statistics(self):
        """عرض نافذة إحصائيات المخزون"""
        try:
            dialog = InventoryStatisticsDialog(self.session, self)
            dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض إحصائيات المخزون: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")


            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def export_statistics_report(self, stats_content):
        """تصدير تقرير الإحصائيات"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإحصائيات", "إحصائيات_المخزون.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            📊 تقرير إحصائيات المخزون
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

{stats_content}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المخزون
═══════════════════════════════════════════════════════════════════════════════
""")

                show_info_message("تم", f"تم تصدير تقرير الإحصائيات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def export_low_stock_report(self):
        """تصدير تقرير المخزون المنخفض"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            items = self.session.query(Inventory).all()
            low_stock_items = [item for item in items if (item.quantity or 0) < (item.min_quantity or 5)]

            if not low_stock_items:
                show_info_message("تقرير المخزون المنخفض", "لا توجد عناصر منخفضة المخزون")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المخزون المنخفض", "المخزون_المنخفض.txt", "Text Files (*.txt)"
            )

            if file_path:
                report_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                            ⚠️ تقرير المخزون المنخفض
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

🔴 عدد العناصر المنخفضة: {len(low_stock_items)}

📋 تفاصيل العناصر المنخفضة:
─────────────────────────────────────────────────────────────────────────────
"""

                for item in low_stock_items:
                    supplier_name = item.supplier.name if item.supplier else "غير محدد"
                    report_content += f"""
🔸 {item.name}
   📊 الكمية الحالية: {int(item.quantity or 0)}
   ⚠️ الحد الأدنى: {int(item.min_quantity or 5)}
   💰 سعر التكلفة: {int(item.cost_price or 0):,} جنيه
   🏪 المورد: {supplier_name}
   📂 الفئة: {item.category or 'غير محدد'}
   ─────────────────────────────────────────────────────────────────────────────
"""

                report_content += """
💡 التوصيات:
─────────────────────────────────────────────────────────────────────────────
• إعادة طلب العناصر المنخفضة فوراً
• مراجعة الحد الأدنى للمخزون
• التواصل مع الموردين لتأكيد التوفر
• مراقبة معدل الاستهلاك

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المخزون
═══════════════════════════════════════════════════════════════════════════════
"""

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                show_info_message("تم", f"تم تصدير تقرير المخزون المنخفض بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def view_stock_alerts(self):
        """عرض تنبيهات المخزون"""
        try:
            items = self.session.query(Inventory).all()
            low_stock_items = [item for item in items if (item.quantity or 0) < (item.min_quantity or 5)]

            # إنشاء نافذة التنبيهات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QListWidgetItem, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("⚠️ تنبيهات المخزون")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout()

            # معلومات التنبيهات
            info_label = QLabel(f"🔴 عدد العناصر المنخفضة: {len(low_stock_items)}")
            info_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; background-color: #f8d7da; border-radius: 5px; color: #721c24;")
            layout.addWidget(info_label)

            # قائمة التنبيهات
            alerts_list = QListWidget()
            alerts_list.setStyleSheet("""
                QListWidget {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 5px;
                }
                QListWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #eee;
                    margin: 2px;
                    border-radius: 3px;
                }
                QListWidget::item:selected {
                    background-color: #fff3cd;
                }
            """)

            if low_stock_items:
                for item in low_stock_items:
                    alert_text = f"⚠️ {item.name} - الكمية: {int(item.quantity or 0)} (الحد الأدنى: {int(item.min_quantity or 5)})"
                    list_item = QListWidgetItem(alert_text)
                    alerts_list.addItem(list_item)
            else:
                no_alerts_item = QListWidgetItem("✅ لا توجد تنبيهات - جميع العناصر فوق الحد الأدنى")
                alerts_list.addItem(no_alerts_item)

            layout.addWidget(alerts_list)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            if low_stock_items:
                export_btn = QPushButton("📤 تصدير التقرير")
                export_btn.clicked.connect(lambda: (dialog.close(), self.export_low_stock_report()))
                buttons_layout.addWidget(export_btn)

            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض التنبيهات: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#06b6d4',
                    'hover_start': '#164e63', 'hover_mid': '#0891b2', 'hover_end': '#06b6d4', 'hover_bottom': '#22d3ee',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#f97316',
                    'hover_start': '#7c2d12', 'hover_mid': '#c2410c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#f97316', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#f97316', 'text': '#ffffff', 'shadow': 'rgba(249, 115, 22, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التصميم على الزر: {str(e)}")





    def create_custom_category_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للفئات مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.category_filter_frame = QFrame()
        self.category_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # إنشاء تخطيط أفقي للإطار
        filter_layout = QHBoxLayout(self.category_filter_frame)
        filter_layout.setContentsMargins(5, 0, 5, 0)
        filter_layout.setSpacing(8)

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # النص الحالي
        self.current_category_label = QLabel("جميع الفئات")
        self.current_category_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_category_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.category_menu_button = QPushButton("")
        self.category_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_category_label, 1)
        filter_layout.addWidget(self.category_menu_button, 0)

        # إنشاء القائمة المنسدلة
        self.category_menu = QMenu(self)
        self.category_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                margin: 2px 5px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة خيارات التصفية مع أيقونات مطابقة للعملاء
        filter_options = [
            ("جميع الفئات", None),
            ("🎨 دهانات", "دهانات"),
            ("🏺 سيراميك", "سيراميك"),
            ("🪵 أخشاب", "أخشاب"),
            ("🚿 أدوات صحية", "أدوات صحية"),
            ("⚡ أدوات كهربائية", "أدوات كهربائية"),
            ("🧱 مواد بناء", "مواد بناء"),
            ("📦 أخرى", "أخرى")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_category_filter(v, t))
            self.category_menu.addAction(action)

        # ربط الأزرار بالقائمة
        self.category_menu_button.clicked.connect(self.show_category_menu)
        self.left_arrow.clicked.connect(self.show_category_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.category_filter_frame.mousePressEvent = self.category_frame_mouse_press_event
        self.current_category_label.mousePressEvent = self.category_frame_mouse_press_event

        # تعيين القيم الافتراضية
        self.current_category_value = None

    def create_custom_stock_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للمخزون المنخفض مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.stock_filter_frame = QFrame()
        self.stock_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # إنشاء تخطيط أفقي للإطار
        stock_layout = QHBoxLayout(self.stock_filter_frame)
        stock_layout.setContentsMargins(5, 0, 5, 0)
        stock_layout.setSpacing(8)

        # سهم يسار
        self.stock_left_arrow = QPushButton("")
        self.stock_left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # النص الحالي
        self.current_stock_label = QLabel("الكل")
        self.current_stock_label.setAlignment(Qt.AlignCenter)
        self.current_stock_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.stock_menu_button = QPushButton("")
        self.stock_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # إضافة العناصر للتخطيط
        stock_layout.addWidget(self.stock_left_arrow, 0)
        stock_layout.addWidget(self.current_stock_label, 1)
        stock_layout.addWidget(self.stock_menu_button, 0)

        # إنشاء القائمة المنسدلة للمخزون
        self.stock_menu = QMenu(self)
        self.stock_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                margin: 2px 5px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إضافة خيارات التصفية للمخزون مع أيقونات مطابقة للعملاء
        stock_options = [
            ("الكل", None),
            ("⚠️ المخزون المنخفض فقط", "low")
        ]

        for text, value in stock_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_stock_filter(v, t))
            self.stock_menu.addAction(action)

        # ربط الأزرار بالقائمة
        self.stock_menu_button.clicked.connect(self.show_stock_menu)
        self.stock_left_arrow.clicked.connect(self.show_stock_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.stock_filter_frame.mousePressEvent = self.stock_frame_mouse_press_event
        self.current_stock_label.mousePressEvent = self.stock_frame_mouse_press_event

        # تعيين القيم الافتراضية
        self.current_stock_value = None

    def show_category_menu(self):
        """عرض قائمة تصفية الفئات"""
        try:
            button = self.sender()
            if button:
                # إذا تم استدعاؤها من زر
                self.category_menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))
            else:
                # إذا تم استدعاؤها من mousePressEvent
                self.category_menu.exec_(self.category_filter_frame.mapToGlobal(self.category_filter_frame.rect().bottomLeft()))
        except Exception as e:
            print(f"خطأ في عرض قائمة الفئات: {str(e)}")
            # عرض القائمة في موقع افتراضي
            self.category_menu.exec_(self.category_filter_frame.mapToGlobal(self.category_filter_frame.rect().bottomLeft()))

    def set_category_filter(self, value, text):
        """تعيين تصفية الفئة"""
        self.current_category_value = value
        self.current_category_label.setText(text)
        self.filter_inventory()

    def category_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية الفئة"""
        self.show_category_menu()

    def show_stock_menu(self):
        """عرض قائمة تصفية المخزون"""
        try:
            button = self.sender()
            if button:
                # إذا تم استدعاؤها من زر
                self.stock_menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))
            else:
                # إذا تم استدعاؤها من mousePressEvent
                self.stock_menu.exec_(self.stock_filter_frame.mapToGlobal(self.stock_filter_frame.rect().bottomLeft()))
        except Exception as e:
            print(f"خطأ في عرض قائمة المخزون: {str(e)}")
            # عرض القائمة في موقع افتراضي
            self.stock_menu.exec_(self.stock_filter_frame.mapToGlobal(self.stock_filter_frame.rect().bottomLeft()))

    def set_stock_filter(self, value, text):
        """تعيين تصفية المخزون"""
        self.current_stock_value = value
        self.current_stock_label.setText(text)
        self.filter_inventory()

    def stock_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية المخزون"""
        self.show_stock_menu()


class InventoryInfoDialog(QDialog):
    """نافذة تفاصيل عنصر المخزون - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, item=None):
        super().__init__(parent)
        self.item = item
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة نافذة المعلومات المرجعية - مطابق تماماً للعملاء"""
        # ═══════════════════════════════════════════════════════════════
        # إعدادات النافذة الأساسية - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        item_name = self.item.name if self.item else "عنصر غير محدد"
        self.setWindowTitle("📦📋 معلومات عنصر المخزون - نظام إدارة المخزون المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setWindowIcon(self.create_window_icon())
        self.customize_title_bar()
        self.setModal(True)
        self.resize(850, 780)  # حجم محسن للعرض الأمثل - مطابق للعملاء

        # ═══════════════════════════════════════════════════════════════
        # تصميم النافذة والخلفية المرجعية - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        self.setStyleSheet(self.get_reference_styling())

        # ═══════════════════════════════════════════════════════════════
        # التخطيط الرئيسي المحسن - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # ═══════════════════════════════════════════════════════════════
        # عنوان النافذة المحسن - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        title_label = QLabel(f"📦 تفاصيل العنصر: {item_name}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 22px;
                font-weight: bold;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 18px 25px;
                margin: 8px 0px 20px 0px;
            }
        """)
        main_layout.addWidget(title_label)

        # ═══════════════════════════════════════════════════════════════
        # منطقة التمرير المحسنة - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                background: transparent;
                padding: 5px;
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                width: 14px;
                border-radius: 7px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(168, 85, 247, 0.6));
                border-radius: 6px;
                min-height: 25px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.9),
                    stop:1 rgba(168, 85, 247, 0.8));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: transparent;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # محتوى المعلومات المحسن - مطابق للعملاء
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.1),
                    stop:0.2 rgba(30, 41, 59, 0.08),
                    stop:0.5 rgba(51, 65, 85, 0.06),
                    stop:0.8 rgba(71, 85, 105, 0.08),
                    stop:1 rgba(100, 116, 139, 0.1));
                border-radius: 12px;
                padding: 10px;
            }
        """)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(25)  # زيادة المسافة بين الأقسام لاستغلال المساحة الإضافية

        # إضافة معلومات العنصر
        self.add_item_info(info_layout)

        scroll_area.setWidget(info_widget)
        main_layout.addWidget(scroll_area)

        # ═══════════════════════════════════════════════════════════════
        # أزرار التحكم المحسنة - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        self.create_control_buttons(main_layout)

        # تطبيق تصميم شريط العنوان
        self.apply_advanced_title_bar_styling()

    def create_window_icon(self):
        """إنشاء أيقونة النافذة - مطابق للعملاء"""
        try:
            from PyQt5.QtGui import QIcon, QPixmap, QPainter, QBrush, QColor
            from PyQt5.QtCore import Qt

            # إنشاء أيقونة بسيطة
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setBrush(QBrush(QColor(59, 130, 246)))
            painter.drawEllipse(4, 4, 24, 24)
            painter.end()

            return QIcon(pixmap)
        except Exception as e:
            print(f"خطأ في إنشاء أيقونة النافذة: {str(e)}")
            return QIcon()

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي - مطابق تماماً للعملاء"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: 4px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """

    def add_item_info(self, layout):
        """إضافة معلومات العنصر إلى التخطيط - مطابق تماماً للنموذج المرجعي"""
        if not self.item:
            return

        # قسم المعلومات الأساسية
        basic_info = [
            ("🔢 المعرف الفريد", f"#{str(self.item.id).zfill(8)}"),
            ("📦 اسم العنصر", self.item.name or "غير محدد"),
            ("🏷️ الفئة", self.item.category or "غير محدد"),
            ("📏 وحدة القياس", self.item.unit or "غير محدد"),
            ("📍 موقع التخزين", self.item.location or "غير محدد")
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية", basic_info)

        # قسم المعلومات المالية والكميات
        financial_info = [
            ("📊 الكمية الحالية", f"{getattr(self.item, 'quantity', 0):,.0f} {self.item.unit or 'وحدة'}"),
            ("⚠️ الحد الأدنى", f"{getattr(self.item, 'min_quantity', 0):,.0f} {self.item.unit or 'وحدة'}"),
            ("💰 سعر التكلفة", f"{getattr(self.item, 'cost_price', 0):,.0f} جنيه"),
            ("💵 سعر البيع", f"{getattr(self.item, 'selling_price', 0):,.0f} جنيه"),
            ("💎 قيمة المخزون", f"{self.get_stock_value():,.0f} جنيه")
        ]
        self.add_info_section(layout, "💰 المعلومات المالية والكميات", financial_info)

        # قسم تفاصيل إضافية
        additional_info = [
            ("🚛 المورد", self.item.supplier.name if self.item.supplier else "غير محدد"),
            ("📈 الربح المتوقع", f"{self.get_expected_profit():,.0f} جنيه"),
            ("📊 حالة المخزون", self.get_stock_status()),
            ("⏰ آخر تحديث", self.item.last_updated.strftime('%Y-%m-%d %H:%M') if hasattr(self.item, 'last_updated') and self.item.last_updated else "غير محدد"),
            ("📋 ملخص العنصر", self.get_item_summary())
        ]
        self.add_info_section(layout, "📊 تفاصيل إضافية", additional_info)

    def add_info_section(self, layout, title, info_list):
        """إضافة قسم معلومات مع التصميم المرجعي"""
        # إطار القسم
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(30, 41, 59, 0.15),
                    stop:0.3 rgba(51, 65, 85, 0.12),
                    stop:0.7 rgba(71, 85, 105, 0.1),
                    stop:1 rgba(100, 116, 139, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                margin: 5px;
                padding: 15px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15, 15, 15, 15)
        section_layout.setSpacing(12)

        # عنوان القسم
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(34, 197, 94, 0.2));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 10px 18px;
                margin-bottom: 12px;
            }
        """)
        section_layout.addWidget(title_label)

        # معلومات القسم
        for label_text, value_text in info_list:
            info_frame = QFrame()
            info_frame.setStyleSheet("""
                QFrame {
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 6px;
                    margin: 2px;
                    padding: 8px;
                }
            """)

            info_layout = QHBoxLayout(info_frame)
            info_layout.setContentsMargins(10, 8, 10, 8)

            # التسمية
            label = QLabel(label_text)
            label.setStyleSheet("""
                QLabel {
                    color: #E2E8F0;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 160px;
                    max-width: 160px;
                }
            """)

            # القيمة
            value = QLabel(str(value_text))
            value.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: normal;
                }
            """)
            value.setWordWrap(True)

            info_layout.addWidget(label)
            info_layout.addWidget(value)
            info_layout.addStretch()

            section_layout.addWidget(info_frame)

        layout.addWidget(section_frame)

    def get_stock_value(self):
        """حساب قيمة المخزون"""
        quantity = getattr(self.item, 'quantity', 0) or 0
        cost_price = getattr(self.item, 'cost_price', 0) or 0
        return quantity * cost_price

    def get_expected_profit(self):
        """حساب الربح المتوقع"""
        quantity = getattr(self.item, 'quantity', 0) or 0
        cost_price = getattr(self.item, 'cost_price', 0) or 0
        selling_price = getattr(self.item, 'selling_price', 0) or 0
        return quantity * (selling_price - cost_price)

    def get_stock_status(self):
        """حالة المخزون"""
        quantity = getattr(self.item, 'quantity', 0) or 0
        min_quantity = getattr(self.item, 'min_quantity', 0) or 0

        if quantity <= 0:
            return "نفد المخزون ❌"
        elif quantity <= min_quantity:
            return "منخفض ⚠️"
        elif quantity <= min_quantity * 2:
            return "متوسط 🟡"
        else:
            return "جيد ✅"

    def get_item_summary(self):
        """ملخص العنصر"""
        item_name = self.item.name or "عنصر غير محدد"
        quantity = getattr(self.item, 'quantity', 0) or 0
        unit = self.item.unit or "وحدة"
        return f"{item_name} - {quantity:,.0f} {unit} متوفر"

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم المحسنة - مطابق تماماً للنموذج المرجعي"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 10px;
                margin: 5px 0;
                min-height: 65px;
                max-height: 70px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # زر الإغلاق - في المقدمة
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(200)
        close_btn.setMaximumHeight(45)
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)
        print_btn.setMaximumHeight(45)
        self.style_advanced_button(print_btn, 'emerald')
        print_btn.clicked.connect(self.print_info)

        # زر تصدير PDF
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(200)
        export_pdf_btn.setMaximumHeight(45)
        self.style_advanced_button(export_pdf_btn, 'info')
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر تعديل الكمية
        adjust_btn = QPushButton("📊 تعديل الكمية")
        adjust_btn.setMinimumWidth(200)
        adjust_btn.setMaximumHeight(45)
        self.style_advanced_button(adjust_btn, 'orange')
        adjust_btn.clicked.connect(self.adjust_quantity)

        # ترتيب الأزرار
        buttons_layout.addWidget(close_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(adjust_btn)

        layout.addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق للنموذج المرجعي"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'emerald': ('#10b981', '#34d399'),
                    'danger': ('#ef4444', '#f87171'),
                    'info': ('#3b82f6', '#60a5fa'),
                    'orange': ('#f97316', '#fb923c')
                }

                if button_type in colors:
                    primary, secondary = colors[button_type]
                    button.setStyleSheet(f"""
                        QPushButton {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {primary}, stop:1 {secondary});
                            color: #FFFFFF;
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            border-radius: 8px;
                            font-size: 15px;
                            font-weight: bold;
                            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                            padding: 10px 14px;
                        }}
                        QPushButton:hover {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {secondary}, stop:1 {primary});
                            border: 3px solid rgba(255, 255, 255, 0.5);
                            transform: translateY(-2px);
                        }}
                        QPushButton:pressed {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {primary}, stop:1 {secondary});
                            border: 1px solid rgba(255, 255, 255, 0.2);
                            transform: translateY(1px);
                        }}
                    """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def print_info(self):
        """طباعة معلومات العنصر"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from datetime import datetime

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y_position = 100
                line_height = 50

                # عنوان التقرير
                painter.setFont(title_font)
                item_name = self.item.name or "عنصر غير محدد"
                painter.drawText(100, y_position, f"تقرير عنصر المخزون: {item_name}")
                y_position += line_height * 2

                # تاريخ الطباعة
                painter.setFont(normal_font)
                painter.drawText(100, y_position, f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                y_position += line_height * 2

                # المعلومات الأساسية
                painter.setFont(header_font)
                painter.drawText(100, y_position, "المعلومات الأساسية:")
                y_position += line_height

                painter.setFont(normal_font)
                basic_info = [
                    f"المعرف: #{str(self.item.id).zfill(8)}",
                    f"اسم العنصر: {item_name}",
                    f"الفئة: {self.item.category or 'غير محدد'}",
                    f"وحدة القياس: {self.item.unit or 'غير محدد'}",
                    f"موقع التخزين: {self.item.location or 'غير محدد'}"
                ]

                for info in basic_info:
                    painter.drawText(120, y_position, info)
                    y_position += line_height

                y_position += line_height

                # المعلومات المالية والكميات
                painter.setFont(header_font)
                painter.drawText(100, y_position, "المعلومات المالية والكميات:")
                y_position += line_height

                painter.setFont(normal_font)
                financial_info = [
                    f"الكمية الحالية: {getattr(self.item, 'quantity', 0):,.0f} {self.item.unit or 'وحدة'}",
                    f"الحد الأدنى: {getattr(self.item, 'min_quantity', 0):,.0f} {self.item.unit or 'وحدة'}",
                    f"سعر التكلفة: {getattr(self.item, 'cost_price', 0):,.0f} جنيه",
                    f"سعر البيع: {getattr(self.item, 'selling_price', 0):,.0f} جنيه",
                    f"قيمة المخزون: {self.get_stock_value():,.0f} جنيه"
                ]

                for info in financial_info:
                    painter.drawText(120, y_position, info)
                    y_position += line_height

                painter.end()
                show_info_message("نجح", "تم طباعة تفاصيل العنصر بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"خطأ في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات العنصر إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont
            from datetime import datetime
            import os

            # اختيار مكان الحفظ
            item_name = self.item.name or "عنصر_غير_محدد"
            default_filename = f"عنصر_مخزون_{item_name}_{self.item.id}.pdf"

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير عنصر المخزون",
                default_filename,
                "PDF Files (*.pdf)"
            )

            if file_path:
                printer = QPrinter()
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)

                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y_position = 100
                line_height = 50

                # عنوان التقرير
                painter.setFont(title_font)
                painter.drawText(100, y_position, f"تقرير عنصر المخزون: {item_name}")
                y_position += line_height * 2

                # تاريخ التصدير
                painter.setFont(normal_font)
                painter.drawText(100, y_position, f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                y_position += line_height * 2

                # المعلومات التفصيلية
                sections = [
                    ("المعلومات الأساسية", [
                        f"المعرف: #{str(self.item.id).zfill(8)}",
                        f"اسم العنصر: {item_name}",
                        f"الفئة: {self.item.category or 'غير محدد'}",
                        f"وحدة القياس: {self.item.unit or 'غير محدد'}",
                        f"موقع التخزين: {self.item.location or 'غير محدد'}"
                    ]),
                    ("المعلومات المالية والكميات", [
                        f"الكمية الحالية: {getattr(self.item, 'quantity', 0):,.0f} {self.item.unit or 'وحدة'}",
                        f"الحد الأدنى: {getattr(self.item, 'min_quantity', 0):,.0f} {self.item.unit or 'وحدة'}",
                        f"سعر التكلفة: {getattr(self.item, 'cost_price', 0):,.0f} جنيه",
                        f"سعر البيع: {getattr(self.item, 'selling_price', 0):,.0f} جنيه",
                        f"قيمة المخزون: {self.get_stock_value():,.0f} جنيه"
                    ]),
                    ("تفاصيل إضافية", [
                        f"المورد: {self.item.supplier.name if self.item.supplier else 'غير محدد'}",
                        f"الربح المتوقع: {self.get_expected_profit():,.0f} جنيه",
                        f"حالة المخزون: {self.get_stock_status()}",
                        f"ملخص العنصر: {self.get_item_summary()}"
                    ])
                ]

                for section_title, section_info in sections:
                    painter.setFont(header_font)
                    painter.drawText(100, y_position, section_title + ":")
                    y_position += line_height

                    painter.setFont(normal_font)
                    for info in section_info:
                        painter.drawText(120, y_position, info)
                        y_position += line_height

                    y_position += line_height // 2

                painter.end()
                show_info_message("نجح", f"تم تصدير تقرير العنصر إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"فشل في التصدير: {str(e)}")

    def adjust_quantity(self):
        """فتح نافذة تعديل الكمية"""
        try:
            from PyQt5.QtWidgets import QInputDialog

            current_quantity = getattr(self.item, 'quantity', 0) or 0
            new_quantity, ok = QInputDialog.getDouble(
                self,
                "تعديل الكمية",
                f"الكمية الحالية: {current_quantity:,.0f} {self.item.unit or 'وحدة'}\n\nأدخل الكمية الجديدة:",
                current_quantity,
                0,
                999999,
                0
            )

            if ok and new_quantity != current_quantity:
                self.item.quantity = new_quantity
                if self.parent_widget and hasattr(self.parent_widget, 'session'):
                    self.parent_widget.session.commit()
                    if hasattr(self.parent_widget, 'refresh_data'):
                        self.parent_widget.refresh_data()

                show_info_message("تم", f"تم تحديث الكمية إلى {new_quantity:,.0f} {self.item.unit or 'وحدة'}")
                self.setup_ui()  # تحديث النافذة

        except Exception as e:
            show_error_message("خطأ", f"فشل في تعديل الكمية: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {str(e)}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان المتطور"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {str(e)}")


class InventoryStatisticsDialog(QDialog):
    """نافذة إحصائيات المخزون مطابقة للعملاء والموردين والعمال والمشاريع والعقارات"""

    def __init__(self, session, parent=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة المتطورة"""
        # إعداد النافذة الأساسي مع شريط عنوان موحد
        self.setWindowTitle("📊 إحصائيات المخزون - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(600, 420)  # نفس ارتفاع نافذة العملاء

        # تخصيص شريط العنوان الموحد
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي المضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)  # تقليل الهوامش
        layout.setSpacing(12)  # تقليل المسافات من 20 إلى 12

        # العنوان الرئيسي المطور بدون إطار - مطابق للعملاء
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 10px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(8)

        # الأيقونة والعنوان الرئيسي
        main_title = QLabel("📊 إحصائيات المخزون")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 30px;
                font-weight: bold;
                background: transparent;
                text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                padding: 10px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel("تقرير شامل عن حالة المخزون والكميات")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # حساب الإحصائيات
        self.calculate_statistics()

        # إنشاء قائمة الإحصائيات المضغوطة
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(8)  # تقليل المسافة أكثر من 15 إلى 8
        stats_layout.setContentsMargins(20, 10, 20, 10)  # تقليل الهوامش أكثر

        # إنشاء قائمة الإحصائيات
        stats_items = [
            ("📦", "إجمالي عناصر المخزون المسجلة", str(self.total_items), "#3B82F6"),
            ("📊", "إجمالي الكميات المتوفرة", str(self.total_quantity), "#10B981"),
            ("⚠️", "عناصر منخفضة المخزون", str(self.low_stock_items), "#F59E0B"),
            ("🔴", "عناصر نفدت من المخزون", str(self.out_of_stock_items), "#EF4444"),
            ("💰", "إجمالي القيمة التقديرية", format_currency(self.total_value), "#10B981")
        ]

        for icon, title, value, color in stats_items:
            # إنشاء عنصر مضغوط بدون إطارات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;  /* تقليل من 8px إلى 5px */
                    margin: 1px;   /* تقليل من 2px إلى 1px */
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(12)  # تقليل من 15 إلى 12
            item_layout.setContentsMargins(12, 5, 12, 5)  # تقليل الهوامش

            # الأيقونة بدون إطارات
            icon_label = QLabel(icon)
            icon_label.setFixedSize(28, 28)  # تصغير أكثر
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;  /* تصغير أكثر */
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 5px;
                }}
            """)
            item_layout.addWidget(icon_label)

            # العنوان المطور مع وصف مفصل
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    padding: 4px 8px;
                }
            """)
            item_layout.addWidget(title_label)

            # مساحة فارغة للدفع
            item_layout.addStretch()

            # القيمة بدون إطارات
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 6px 12px;
                    min-width: 70px;
                }}
            """)
            value_label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(value_label)

            stats_layout.addWidget(item_widget)

        layout.addLayout(stats_layout)

        # أزرار التحكم مطابقة للعملاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر تصدير PDF
        export_pdf_button = QPushButton("📄 تصدير PDF")
        export_pdf_button.clicked.connect(self.export_statistics_to_pdf)
        export_pdf_button.setMinimumHeight(45)
        self.style_advanced_button(export_pdf_button, 'info')

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        close_button.setMinimumHeight(45)
        self.style_advanced_button(close_button, 'danger')

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(export_pdf_button)

        layout.addLayout(buttons_layout)

    def calculate_statistics(self):
        """حساب إحصائيات المخزون"""
        try:
            # حساب إجمالي العناصر
            self.total_items = self.session.query(Inventory).count()

            # حساب إجمالي الكميات
            total_quantity_result = self.session.query(func.sum(Inventory.quantity)).scalar()
            self.total_quantity = int(total_quantity_result or 0)

            # حساب العناصر منخفضة المخزون (أقل من 10)
            self.low_stock_items = self.session.query(Inventory).filter(Inventory.quantity < 10).count()

            # حساب العناصر النافدة (كمية = 0)
            self.out_of_stock_items = self.session.query(Inventory).filter(Inventory.quantity == 0).count()

            # حساب إجمالي القيمة (الكمية × سعر التكلفة)
            items = self.session.query(Inventory).all()
            self.total_value = sum((item.quantity or 0) * (item.cost_price or 0) for item in items)

        except Exception as e:
            print(f"خطأ في حساب إحصائيات المخزون: {e}")
            self.total_items = 0
            self.total_quantity = 0
            self.low_stock_items = 0
            self.out_of_stock_items = 0
            self.total_value = 0

    def export_statistics_to_pdf(self):
        """تصدير الإحصائيات إلى PDF - دالة مؤقتة"""
        QMessageBox.information(self, "تصدير PDF", "ميزة تصدير PDF قيد التطوير")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار مطابق للعملاء"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")

    # دوال التصدير المتقدمة الجديدة
    def export_excel_advanced(self):
        """تصدير Excel متقدم للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", f"مخزون_excel_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                items = self.session.query(Inventory).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تصدير Excel متقدم للمخزون'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي العناصر: {len(items)}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    writer.writerow(['الرقم', 'اسم العنصر', 'الفئة', 'الكمية', 'الوحدة', 'سعر التكلفة', 'سعر البيع', 'الربح', 'المورد', 'الموقع'])

                    # البيانات
                    for item in items:
                        supplier_name = item.supplier.name if item.supplier else 'غير محدد'
                        profit = (item.selling_price or 0) - (item.cost_price or 0)

                        writer.writerow([
                            item.id,
                            item.name or 'غير محدد',
                            item.category or 'غير محدد',
                            f"{item.quantity:.0f}" if item.quantity else '0',
                            item.unit or 'قطعة',
                            f"{item.cost_price:.2f}" if item.cost_price else '0.00',
                            f"{item.selling_price:.2f}" if item.selling_price else '0.00',
                            f"{profit:.2f}",
                            supplier_name,
                            item.location or 'غير محدد'
                        ])

                show_info_message("تم", f"تم تصدير Excel بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير Excel: {str(e)}")

    def export_csv_advanced(self):
        """تصدير CSV شامل للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV", f"مخزون_csv_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                items = self.session.query(Inventory).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير مع إحصائيات
                    writer.writerow(['تصدير CSV شامل للمخزون'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])

                    # إحصائيات سريعة
                    total_value = sum((item.selling_price or 0) * (item.quantity or 0) for item in items)
                    total_cost = sum((item.cost_price or 0) * (item.quantity or 0) for item in items)
                    total_profit = total_value - total_cost
                    low_stock_count = len([item for item in items if (item.quantity or 0) <= (item.min_quantity or 0)])

                    writer.writerow([f'إجمالي العناصر: {len(items)}'])
                    writer.writerow([f'إجمالي قيمة المخزون: {total_value:.2f} جنيه'])
                    writer.writerow([f'إجمالي تكلفة المخزون: {total_cost:.2f} جنيه'])
                    writer.writerow([f'إجمالي الربح المتوقع: {total_profit:.2f} جنيه'])
                    writer.writerow([f'عناصر تحت الحد الأدنى: {low_stock_count}'])
                    writer.writerow([])

                    # رؤوس الأعمدة الشاملة
                    writer.writerow(['الرقم', 'اسم العنصر', 'الفئة', 'الكمية', 'الحد الأدنى', 'الوحدة', 'سعر التكلفة', 'سعر البيع', 'الربح للوحدة', 'إجمالي القيمة', 'المورد', 'الموقع', 'حالة المخزون'])

                    # البيانات الشاملة
                    for item in items:
                        supplier_name = item.supplier.name if item.supplier else 'غير محدد'
                        profit_per_unit = (item.selling_price or 0) - (item.cost_price or 0)
                        total_item_value = (item.selling_price or 0) * (item.quantity or 0)

                        # تحديد حالة المخزون
                        if (item.quantity or 0) <= 0:
                            stock_status = 'نفد المخزون'
                        elif (item.quantity or 0) <= (item.min_quantity or 0):
                            stock_status = 'تحت الحد الأدنى'
                        else:
                            stock_status = 'متوفر'

                        writer.writerow([
                            item.id,
                            item.name or 'غير محدد',
                            item.category or 'غير محدد',
                            f"{item.quantity:.0f}" if item.quantity else '0',
                            f"{item.min_quantity:.0f}" if item.min_quantity else '0',
                            item.unit or 'قطعة',
                            f"{item.cost_price:.2f}" if item.cost_price else '0.00',
                            f"{item.selling_price:.2f}" if item.selling_price else '0.00',
                            f"{profit_per_unit:.2f}",
                            f"{total_item_value:.2f}",
                            supplier_name,
                            item.location or 'غير محدد',
                            stock_status
                        ])

                show_info_message("تم", f"تم تصدير CSV بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير CSV: {str(e)}")

    def export_pdf_advanced(self):
        """تصدير PDF تفصيلي للمخزون"""
        show_info_message("قريباً", "ميزة تصدير PDF المتقدم قيد التطوير")

    def export_stock_report(self):
        """تقرير المخزون المتقدم"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المخزون", f"تقرير_مخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                items = self.session.query(Inventory).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير المخزون المتقدم'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # تحليل الفئات
                    categories = {}
                    for item in items:
                        category = item.category or 'غير محدد'
                        if category not in categories:
                            categories[category] = {'count': 0, 'total_value': 0, 'total_quantity': 0}
                        categories[category]['count'] += 1
                        categories[category]['total_value'] += (item.selling_price or 0) * (item.quantity or 0)
                        categories[category]['total_quantity'] += item.quantity or 0

                    writer.writerow(['تحليل الفئات:'])
                    writer.writerow(['الفئة', 'عدد العناصر', 'إجمالي الكمية', 'إجمالي القيمة'])

                    for category, data in sorted(categories.items(), key=lambda x: x[1]['total_value'], reverse=True):
                        writer.writerow([
                            category,
                            data['count'],
                            f"{data['total_quantity']:.0f}",
                            f"{data['total_value']:.2f}"
                        ])

                    writer.writerow([])

                    # العناصر الأكثر قيمة
                    writer.writerow(['أعلى 10 عناصر قيمة:'])
                    writer.writerow(['اسم العنصر', 'الكمية', 'سعر البيع', 'إجمالي القيمة'])

                    top_items = sorted(items, key=lambda x: (x.selling_price or 0) * (x.quantity or 0), reverse=True)[:10]
                    for item in top_items:
                        total_value = (item.selling_price or 0) * (item.quantity or 0)
                        writer.writerow([
                            item.name or 'غير محدد',
                            f"{item.quantity:.0f}" if item.quantity else '0',
                            f"{item.selling_price:.2f}" if item.selling_price else '0.00',
                            f"{total_value:.2f}"
                        ])

                show_info_message("تم", f"تم إنشاء تقرير المخزون بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء تقرير المخزون: {str(e)}")

    def export_low_stock_report(self):
        """تقرير النواقص والعناصر تحت الحد الأدنى"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير النواقص", f"تقرير_نواقص_مخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # العناصر تحت الحد الأدنى أو نفدت
                low_stock_items = self.session.query(Inventory).filter(
                    (Inventory.quantity <= Inventory.min_quantity) | (Inventory.quantity <= 0)
                ).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير النواقص والعناصر تحت الحد الأدنى'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # إحصائيات النواقص
                    out_of_stock = [item for item in low_stock_items if (item.quantity or 0) <= 0]
                    low_stock = [item for item in low_stock_items if (item.quantity or 0) > 0 and (item.quantity or 0) <= (item.min_quantity or 0)]

                    writer.writerow(['إحصائيات النواقص:'])
                    writer.writerow([f'إجمالي العناصر المتأثرة: {len(low_stock_items)}'])
                    writer.writerow([f'عناصر نفد مخزونها: {len(out_of_stock)}'])
                    writer.writerow([f'عناصر تحت الحد الأدنى: {len(low_stock)}'])
                    writer.writerow([])

                    if out_of_stock:
                        writer.writerow(['العناصر التي نفد مخزونها:'])
                        writer.writerow(['اسم العنصر', 'الفئة', 'الكمية الحالية', 'الحد الأدنى', 'المورد', 'الموقع'])

                        for item in out_of_stock:
                            supplier_name = item.supplier.name if item.supplier else 'غير محدد'
                            writer.writerow([
                                item.name or 'غير محدد',
                                item.category or 'غير محدد',
                                f"{item.quantity:.0f}" if item.quantity else '0',
                                f"{item.min_quantity:.0f}" if item.min_quantity else '0',
                                supplier_name,
                                item.location or 'غير محدد'
                            ])
                        writer.writerow([])

                    if low_stock:
                        writer.writerow(['العناصر تحت الحد الأدنى:'])
                        writer.writerow(['اسم العنصر', 'الفئة', 'الكمية الحالية', 'الحد الأدنى', 'الكمية المطلوبة', 'المورد', 'الموقع'])

                        for item in low_stock:
                            supplier_name = item.supplier.name if item.supplier else 'غير محدد'
                            required_qty = (item.min_quantity or 0) - (item.quantity or 0)
                            writer.writerow([
                                item.name or 'غير محدد',
                                item.category or 'غير محدد',
                                f"{item.quantity:.0f}" if item.quantity else '0',
                                f"{item.min_quantity:.0f}" if item.min_quantity else '0',
                                f"{required_qty:.0f}" if required_qty > 0 else '0',
                                supplier_name,
                                item.location or 'غير محدد'
                            ])

                show_info_message("تم", f"تم إنشاء تقرير النواقص بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء تقرير النواقص: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار - مطابق للأقسام الأخرى"""
        try:
            # استخدام دالة التصميم الموحدة
            UnifiedStyles.apply_advanced_button_style(button, button_type, has_menu)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")
            # تطبيق تصميم بديل بسيط
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: #3b82f6;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #2563eb;
                }}
            """)

    def export_valuation_report(self):
        """تقرير التقييم المالي للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير التقييم", f"تقرير_تقييم_مخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                items = self.session.query(Inventory).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير التقييم المالي للمخزون'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # الإحصائيات المالية
                    total_cost_value = sum((item.cost_price or 0) * (item.quantity or 0) for item in items)
                    total_selling_value = sum((item.selling_price or 0) * (item.quantity or 0) for item in items)
                    total_profit = total_selling_value - total_cost_value
                    profit_margin = (total_profit / total_selling_value * 100) if total_selling_value > 0 else 0

                    writer.writerow(['الإحصائيات المالية:'])
                    writer.writerow([f'إجمالي تكلفة المخزون: {total_cost_value:.2f} جنيه'])
                    writer.writerow([f'إجمالي قيمة البيع: {total_selling_value:.2f} جنيه'])
                    writer.writerow([f'إجمالي الربح المتوقع: {total_profit:.2f} جنيه'])
                    writer.writerow([f'هامش الربح: {profit_margin:.1f}%'])
                    writer.writerow([])

                    # تفاصيل التقييم
                    writer.writerow(['تفاصيل التقييم:'])
                    writer.writerow(['اسم العنصر', 'الكمية', 'تكلفة الوحدة', 'سعر البيع', 'إجمالي التكلفة', 'إجمالي البيع', 'الربح', 'هامش الربح%'])

                    for item in sorted(items, key=lambda x: ((x.selling_price or 0) - (x.cost_price or 0)) * (x.quantity or 0), reverse=True):
                        cost_total = (item.cost_price or 0) * (item.quantity or 0)
                        selling_total = (item.selling_price or 0) * (item.quantity or 0)
                        profit = selling_total - cost_total
                        margin = (profit / selling_total * 100) if selling_total > 0 else 0

                        writer.writerow([
                            item.name or 'غير محدد',
                            f"{item.quantity:.0f}" if item.quantity else '0',
                            f"{item.cost_price:.2f}" if item.cost_price else '0.00',
                            f"{item.selling_price:.2f}" if item.selling_price else '0.00',
                            f"{cost_total:.2f}",
                            f"{selling_total:.2f}",
                            f"{profit:.2f}",
                            f"{margin:.1f}%"
                        ])

                show_info_message("تم", f"تم إنشاء تقرير التقييم بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء تقرير التقييم: {str(e)}")

    def export_custom(self):
        """تصدير مخصص للمخزون مع خيارات متقدمة - مطابق تماماً للعملاء"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QGroupBox, QPushButton, QLabel

            # إنشاء نافذة الخيارات المخصصة مع ألوان الإحصائيات - مطابق للعملاء
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 تصدير مخصص - خيارات متقدمة")
            dialog.setModal(True)
            dialog.resize(450, 500)

            # إزالة علامة الاستفهام وتحسين شريط العنوان مطابق للبرنامج
            dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

            # تخصيص شريط العنوان مطابق للبرنامج
            try:
                from ui.title_bar_utils import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(dialog)
            except Exception as e:
                print(f"تحذير: فشل في تطبيق تصميم شريط العنوان للنافذة الحوارية: {e}")

            # تطبيق نمط النافذة مطابق تماماً لنافذة الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            # التخطيط الرئيسي مضغوط
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(15, 10, 15, 10)
            layout.setSpacing(8)

            # العنوان الرئيسي مضغوط
            title_container = QWidget()
            title_container.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                }
            """)

            title_inner_layout = QVBoxLayout(title_container)
            title_inner_layout.setContentsMargins(0, 0, 0, 0)
            title_inner_layout.setSpacing(3)

            # الأيقونة والعنوان الرئيسي مضغوط
            main_title = QLabel("🔧 تصدير مخصص للمخزون")
            main_title.setAlignment(Qt.AlignCenter)
            main_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 22px;
                    font-weight: bold;
                    background: transparent;
                    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                    padding: 5px;
                }
            """)

            # العنوان الفرعي التوضيحي مضغوط
            subtitle = QLabel("اختر البيانات المراد تصديرها بدقة")
            subtitle.setAlignment(Qt.AlignCenter)
            subtitle.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    font-weight: normal;
                    background: transparent;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 2px;
                }
            """)

            title_inner_layout.addWidget(main_title)
            title_inner_layout.addWidget(subtitle)
            layout.addWidget(title_container)

            # مجموعة البيانات الأساسية مطابقة لنافذة الإحصائيات
            basic_group = QWidget()
            basic_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            basic_main_layout = QVBoxLayout(basic_group)
            basic_main_layout.setSpacing(5)
            basic_main_layout.setContentsMargins(15, 5, 15, 5)

            # عنوان المجموعة مضغوط ومتوسط
            basic_title = QLabel("📋 البيانات الأساسية")
            basic_title.setAlignment(Qt.AlignCenter)
            basic_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            basic_main_layout.addWidget(basic_title)

            basic_layout = QVBoxLayout()
            basic_layout.setSpacing(3)

            self.export_id = QCheckBox("🆔 الرقم التعريفي")
            self.export_name = QCheckBox("📦 اسم العنصر")
            self.export_category = QCheckBox("🏷️ الفئة")
            self.export_quantity = QCheckBox("📊 الكمية")
            self.export_unit = QCheckBox("📏 الوحدة")

            # تحديد افتراضي
            self.export_name.setChecked(True)
            self.export_quantity.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات
            checkboxes_data = [
                (self.export_id, "#3B82F6"),
                (self.export_name, "#10B981"),
                (self.export_category, "#F59E0B"),
                (self.export_quantity, "#EF4444"),
                (self.export_unit, "#8B5CF6")
            ]

            for checkbox, color in checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 2px;
                        margin: 0px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(8)
                item_layout.setContentsMargins(8, 3, 8, 3)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)
                item_layout.addWidget(checkbox)
                basic_layout.addWidget(item_widget)

            basic_main_layout.addLayout(basic_layout)
            layout.addWidget(basic_group)

            # مجموعة البيانات المالية
            financial_group = QWidget()
            financial_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            financial_main_layout = QVBoxLayout(financial_group)
            financial_main_layout.setSpacing(5)
            financial_main_layout.setContentsMargins(15, 5, 15, 5)

            financial_title = QLabel("💰 البيانات المالية")
            financial_title.setAlignment(Qt.AlignCenter)
            financial_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            financial_main_layout.addWidget(financial_title)

            financial_layout = QVBoxLayout()
            financial_layout.setSpacing(3)

            self.export_cost_price = QCheckBox("💰 سعر التكلفة")
            self.export_selling_price = QCheckBox("💵 سعر البيع")
            self.export_profit = QCheckBox("📈 الربح")

            self.export_selling_price.setChecked(True)

            financial_checkboxes_data = [
                (self.export_cost_price, "#F59E0B"),
                (self.export_selling_price, "#10B981"),
                (self.export_profit, "#EF4444")
            ]

            for checkbox, color in financial_checkboxes_data:
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(12)
                item_layout.setContentsMargins(12, 5, 12, 5)

                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)
                item_layout.addWidget(checkbox)
                financial_layout.addWidget(item_widget)

            financial_main_layout.addLayout(financial_layout)
            layout.addWidget(financial_group)

            # مجموعة البيانات الإضافية
            additional_group = QWidget()
            additional_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            additional_main_layout = QVBoxLayout(additional_group)
            additional_main_layout.setSpacing(5)
            additional_main_layout.setContentsMargins(15, 5, 15, 5)

            additional_title = QLabel("📝 البيانات الإضافية")
            additional_title.setAlignment(Qt.AlignCenter)
            additional_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            additional_main_layout.addWidget(additional_title)

            additional_layout = QVBoxLayout()
            additional_layout.setSpacing(3)

            self.export_supplier = QCheckBox("🚛 المورد")
            self.export_location = QCheckBox("📍 الموقع")
            self.export_min_quantity = QCheckBox("⚠️ الحد الأدنى")
            self.export_statistics = QCheckBox("📊 إضافة الإحصائيات")

            additional_checkboxes_data = [
                (self.export_supplier, "#8B5CF6"),
                (self.export_location, "#06B6D4"),
                (self.export_min_quantity, "#F59E0B"),
                (self.export_statistics, "#F97316")
            ]

            for checkbox, color in additional_checkboxes_data:
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(12)
                item_layout.setContentsMargins(12, 5, 12, 5)

                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)
                item_layout.addWidget(checkbox)
                additional_layout.addWidget(item_widget)

            additional_main_layout.addLayout(additional_layout)
            layout.addWidget(additional_group)

            # أزرار التحكم مطابقة لنافذة الإحصائيات
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)

            # زر الإلغاء مطابق للبرنامج الرئيسي
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.reject)
            cancel_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(cancel_btn, 'danger')

            # زر التصدير مطابق للبرنامج الرئيسي
            export_btn = QPushButton("📤 تصدير")
            export_btn.clicked.connect(lambda: self.perform_custom_export_inventory(dialog))
            export_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(export_btn, 'emerald')

            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addWidget(export_btn)
            layout.addLayout(buttons_layout)

            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير المخصص: {str(e)}")

    def perform_custom_export_inventory(self, dialog):
        """تنفيذ التصدير المخصص للمخزون - مطابق للعملاء"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التصدير المخصص", f"تصدير_مخصص_مخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                items = self.session.query(Inventory).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # إضافة الإحصائيات إذا تم اختيارها
                    if self.export_statistics.isChecked():
                        writer.writerow(['تصدير مخصص للمخزون'])
                        writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                        writer.writerow([f'إجمالي العناصر: {len(items)}'])
                        writer.writerow([])

                    # إنشاء رؤوس الأعمدة حسب الاختيار
                    headers = []
                    if self.export_id.isChecked():
                        headers.append('الرقم التعريفي')
                    if self.export_name.isChecked():
                        headers.append('اسم العنصر')
                    if self.export_category.isChecked():
                        headers.append('الفئة')
                    if self.export_quantity.isChecked():
                        headers.append('الكمية')
                    if self.export_unit.isChecked():
                        headers.append('الوحدة')
                    if self.export_cost_price.isChecked():
                        headers.append('سعر التكلفة')
                    if self.export_selling_price.isChecked():
                        headers.append('سعر البيع')
                    if self.export_profit.isChecked():
                        headers.append('الربح')
                    if self.export_supplier.isChecked():
                        headers.append('المورد')
                    if self.export_location.isChecked():
                        headers.append('الموقع')
                    if self.export_min_quantity.isChecked():
                        headers.append('الحد الأدنى')

                    writer.writerow(headers)

                    # كتابة البيانات
                    for item in items:
                        row = []
                        if self.export_id.isChecked():
                            row.append(item.id)
                        if self.export_name.isChecked():
                            row.append(item.name or 'غير محدد')
                        if self.export_category.isChecked():
                            row.append(item.category or 'غير محدد')
                        if self.export_quantity.isChecked():
                            row.append(f"{item.quantity:.0f}" if item.quantity else '0')
                        if self.export_unit.isChecked():
                            row.append(item.unit or 'قطعة')
                        if self.export_cost_price.isChecked():
                            row.append(f"{item.cost_price:.2f}" if item.cost_price else '0.00')
                        if self.export_selling_price.isChecked():
                            row.append(f"{item.selling_price:.2f}" if item.selling_price else '0.00')
                        if self.export_profit.isChecked():
                            profit = (item.selling_price or 0) - (item.cost_price or 0)
                            row.append(f"{profit:.2f}")
                        if self.export_supplier.isChecked():
                            row.append(item.supplier.name if item.supplier else 'غير محدد')
                        if self.export_location.isChecked():
                            row.append(item.location or 'غير محدد')
                        if self.export_min_quantity.isChecked():
                            row.append(f"{item.min_quantity:.0f}" if item.min_quantity else '0')

                        writer.writerow(row)

                dialog.accept()
                show_info_message("تم", f"تم التصدير المخصص بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير المخصص: {str(e)}")

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_مخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                items = self.session.query(Inventory).all()
                backup_data = {
                    'backup_info': {
                        'created_at': datetime.now().isoformat(),
                        'total_records': len(items),
                        'backup_type': 'inventory_full_backup',
                        'version': '1.0'
                    },
                    'inventory': []
                }

                for item in items:
                    item_data = {
                        'id': item.id,
                        'name': item.name,
                        'category': item.category,
                        'unit': item.unit,
                        'quantity': float(item.quantity) if item.quantity else 0.0,
                        'min_quantity': float(item.min_quantity) if item.min_quantity else 0.0,
                        'cost_price': float(item.cost_price) if item.cost_price else 0.0,
                        'selling_price': float(item.selling_price) if item.selling_price else 0.0,
                        'supplier_id': item.supplier_id if item.supplier_id else None,
                        'supplier_name': item.supplier.name if item.supplier else None,
                        'location': item.location,
                        'notes': item.notes,
                        'last_updated': item.last_updated.isoformat() if hasattr(item, 'last_updated') and item.last_updated else datetime.now().isoformat()
                    }
                    backup_data['inventory'].append(item_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                show_info_message("تم", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(items)} عنصر")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            import json
            from datetime import datetime

            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختر النسخة الاحتياطية", "", "JSON Files (*.json)"
            )

            if file_path:
                # تأكيد الاستعادة
                reply = QMessageBox.question(
                    self, "تأكيد الاستعادة",
                    "هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\nسيتم دمج البيانات مع الموجودة حالياً!",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    if 'inventory' in backup_data:
                        restored_count = 0
                        updated_count = 0

                        for item_data in backup_data['inventory']:
                            # التحقق من وجود العنصر
                            existing = self.session.query(Inventory).filter_by(name=item_data.get('name')).first()

                            if existing:
                                # تحديث العنصر الموجود
                                existing.category = item_data.get('category')
                                existing.unit = item_data.get('unit')
                                existing.quantity = item_data.get('quantity', 0)
                                existing.min_quantity = item_data.get('min_quantity', 0)
                                existing.cost_price = item_data.get('cost_price', 0)
                                existing.selling_price = item_data.get('selling_price', 0)
                                existing.location = item_data.get('location')
                                existing.notes = item_data.get('notes')
                                updated_count += 1
                            else:
                                # إنشاء عنصر جديد
                                new_item = Inventory(
                                    name=item_data.get('name'),
                                    category=item_data.get('category'),
                                    unit=item_data.get('unit'),
                                    quantity=item_data.get('quantity', 0),
                                    min_quantity=item_data.get('min_quantity', 0),
                                    cost_price=item_data.get('cost_price', 0),
                                    selling_price=item_data.get('selling_price', 0),
                                    supplier_id=item_data.get('supplier_id'),
                                    location=item_data.get('location'),
                                    notes=item_data.get('notes')
                                )
                                self.session.add(new_item)
                                restored_count += 1

                        self.session.commit()
                        self.load_inventory()  # إعادة تحميل البيانات

                        show_info_message("تم", f"تم استعادة النسخة الاحتياطية بنجاح!\n\nتم إضافة {restored_count} عنصر جديد\nتم تحديث {updated_count} عنصر موجود")
                    else:
                        show_error_message("خطأ", "ملف النسخة الاحتياطية غير صالح!")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")
