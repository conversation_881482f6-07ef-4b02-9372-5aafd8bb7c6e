#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النسخ الاحتياطي والاستعادة للعملاء
"""

import json
import datetime
from database import get_session, Client

def create_sample_backup():
    """إنشاء نسخة احتياطية تجريبية للاختبار"""
    
    # بيانات عملاء تجريبية
    sample_clients = [
        {
            "id": 1,
            "name": "أحمد محمد علي",
            "phone": "01012345678",
            "address": "شارع النيل، القاهرة",
            "email": "<EMAIL>",
            "balance": 5000.0,
            "notes": "عميل مميز",
            "created_at": "2024-01-15T10:30:00",
            "metadata": {
                "balance_category": "positive",
                "client_tier": "regular",
                "has_contact_info": True,
                "has_address": True,
                "has_notes": True
            }
        },
        {
            "id": 2,
            "name": "فاطمة أحمد حسن",
            "phone": "01098765432",
            "address": "شارع الجمهورية، الإسكندرية",
            "email": "<EMAIL>",
            "balance": 15000.0,
            "notes": "عميل VIP",
            "created_at": "2024-02-20T14:45:00",
            "metadata": {
                "balance_category": "positive",
                "client_tier": "vip",
                "has_contact_info": True,
                "has_address": True,
                "has_notes": True
            }
        },
        {
            "id": 3,
            "name": "محمد عبدالله",
            "phone": "01155555555",
            "address": "شارع المعز، القاهرة",
            "email": "",
            "balance": -2000.0,
            "notes": "",
            "created_at": "2024-03-10T09:15:00",
            "metadata": {
                "balance_category": "negative",
                "client_tier": "debtor",
                "has_contact_info": True,
                "has_address": True,
                "has_notes": False
            }
        },
        {
            "id": 4,
            "name": "سارة محمود",
            "phone": "01077777777",
            "address": "شارع الهرم، الجيزة",
            "email": "<EMAIL>",
            "balance": 0.0,
            "notes": "عميل جديد",
            "created_at": "2024-04-05T16:20:00",
            "metadata": {
                "balance_category": "zero",
                "client_tier": "regular",
                "has_contact_info": True,
                "has_address": True,
                "has_notes": True
            }
        },
        {
            "id": 5,
            "name": "عمر حسام",
            "phone": "01033333333",
            "address": "شارع الثورة، المنصورة",
            "email": "<EMAIL>",
            "balance": 8500.0,
            "notes": "عميل منتظم",
            "created_at": "2024-05-12T11:30:00",
            "metadata": {
                "balance_category": "positive",
                "client_tier": "regular",
                "has_contact_info": True,
                "has_address": True,
                "has_notes": True
            }
        }
    ]
    
    # إنشاء بيانات النسخة الاحتياطية
    backup_data = {
        "backup_info": {
            "created_at": datetime.datetime.now().isoformat(),
            "system_version": "1.0",
            "total_records": len(sample_clients),
            "backup_type": "full_clients_backup"
        },
        "clients_data": sample_clients
    }
    
    # حفظ النسخة الاحتياطية
    filename = f"نسخة_احتياطية_تجريبية_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(backup_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ تم إنشاء النسخة الاحتياطية التجريبية: {filename}")
    print(f"📊 تحتوي على {len(sample_clients)} عميل تجريبي")
    
    return filename

def verify_backup_file(filename):
    """التحقق من صحة ملف النسخة الاحتياطية"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)
        
        print(f"🔍 التحقق من ملف: {filename}")
        
        # التحقق من البنية الأساسية
        if "backup_info" not in backup_data:
            print("❌ معلومات النسخة الاحتياطية مفقودة")
            return False
        
        if "clients_data" not in backup_data:
            print("❌ بيانات العملاء مفقودة")
            return False
        
        backup_info = backup_data["backup_info"]
        clients_data = backup_data["clients_data"]
        
        print(f"📅 تاريخ الإنشاء: {backup_info.get('created_at', 'غير محدد')}")
        print(f"📊 عدد العملاء: {len(clients_data)}")
        print(f"🔄 نوع النسخة: {backup_info.get('backup_type', 'غير محدد')}")
        
        # التحقق من بيانات العملاء
        for i, client in enumerate(clients_data):
            required_fields = ["name", "phone", "address", "email", "balance"]
            for field in required_fields:
                if field not in client:
                    print(f"❌ العميل {i+1}: الحقل '{field}' مفقود")
                    return False
        
        print("✅ النسخة الاحتياطية صحيحة ومكتملة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def show_current_clients():
    """عرض العملاء الحاليين في قاعدة البيانات"""
    session = get_session()
    
    try:
        clients = session.query(Client).all()
        
        print(f"\n📋 العملاء الحاليين في قاعدة البيانات ({len(clients)} عميل):")
        print("-" * 80)
        
        if not clients:
            print("لا يوجد عملاء في قاعدة البيانات")
            return
        
        for client in clients:
            print(f"🆔 {client.id} | 👤 {client.name} | 📞 {client.phone} | 💰 {client.balance:,.2f} جنيه")
        
        print("-" * 80)
        
    except Exception as e:
        print(f"❌ خطأ في قراءة قاعدة البيانات: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    print("🧪 اختبار النسخ الاحتياطي والاستعادة")
    print("=" * 50)
    
    # عرض العملاء الحاليين
    show_current_clients()
    
    # إنشاء نسخة احتياطية تجريبية
    backup_file = create_sample_backup()
    
    # التحقق من النسخة الاحتياطية
    verify_backup_file(backup_file)
    
    print(f"\n💡 يمكنك الآن استخدام الملف '{backup_file}' لاختبار الاستعادة في البرنامج")
    print("📝 خطوات الاختبار:")
    print("1. افتح البرنامج")
    print("2. اذهب إلى قسم العملاء")
    print("3. اضغط على زر التصدير")
    print("4. اختر 'استعادة نسخة احتياطية'")
    print(f"5. اختر الملف: {backup_file}")
    print("6. تأكد من الاستعادة")
