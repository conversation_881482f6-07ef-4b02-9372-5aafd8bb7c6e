import re
import platform
import ctypes
from ctypes import wintypes
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QFrame, QComboBox, QSizePolicy, QMenu, QAction,
                            QDialog, QFormLayout, QTextEdit, QDoubleSpinBox, QMessageBox,
                            QScrollArea, QGraphicsDropShadowEffect, QDateEdit, QGroupBox)
from PyQt5.QtCore import Qt, QTimer, QDate
from PyQt5.QtGui import QFont, QColor, QPainter, QPixmap, QBrush, QPen, QIcon, QRadialGradient

from database import Supplier
from utils import format_currency, show_info_message, show_error_message, qdate_to_datetime
from ui.common_dialogs import WarningDialog
from ui.unified_styles import StyledButton
try:
    from ui.title_bar_utils import TitleBarStyler
except ImportError:
    # إذا لم يكن المودول متاحاً، نستخدم دالة بديلة
    class TitleBarStyler:
        @staticmethod
        def apply_advanced_title_bar_styling(dialog):
            pass


class SuppliersWidget(QWidget):
    """واجهة إدارة الموردين مطابقة للفواتير"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.current_filter_value = None
        self.init_ui()

    def init_ui(self):
        """إنشاء واجهة الموردين مطابقة للفواتير"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)

        # العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🚛 إدارة الموردين المتطورة - نظام شامل ومتقدم لإدارة الموردين مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي للبحث والتصفية متساوي مع الجدول وارتفاع أقل مطابق للفواتير
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(3)  # مسافة أقل بين العناصر

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(4, 0, 4, 0)  # هوامش جانبية أقل
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث مطورة بألوان احترافية مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث باسم المورد، الهاتف، البريد أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_suppliers)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        search_frame.setLayout(top_container)

        # إنشاء الجدول
        self.create_suppliers_table()

        # إنشاء إطار الأزرار
        self.create_action_buttons()

        # إضافة العناصر للتخطيط الرئيسي
        main_layout.addWidget(search_frame)
        main_layout.addWidget(self.suppliers_table, 1)
        main_layout.addWidget(self.buttons_frame)

        self.setLayout(main_layout)

        # تهيئة المتغيرات
        self.current_filter_value = None

        # ربط الأحداث
        self.search_edit.textChanged.connect(self.filter_suppliers)

        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(50, self.refresh_data)

        # تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة
        QTimer.singleShot(75, self.initialize_button_states)

    def create_suppliers_table(self):
        """إنشاء جدول الموردين مطابق للعملاء مع 9 أعمدة"""
        # إنشاء الجدول
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(9)

        # عناوين الأعمدة مع الأيقونات مطابقة للعملاء (الترتيب الجديد مع الملاحظات والتاريخ في النهاية)
        headers = [
            "🔢 ID",
            "🚛 اسم المورد",
            "🏠 العنوان",
            "📧 البريد الإلكتروني",
            "📱 رقم الهاتف",
            "💵 الرصيد",
            "⭐ حالة المورد",
            "📋 الملاحظات",
            "🗓️ تاريخ الإضافة"
        ]

        self.suppliers_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول مطابقة للعملاء
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.SingleSelection)
        self.suppliers_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.suppliers_table.setAlternatingRowColors(False)
        self.suppliers_table.setSortingEnabled(True)

        # إعدادات الصفوف والأعمدة مطابقة للعملاء
        self.suppliers_table.verticalHeader().setDefaultSectionSize(50)
        self.suppliers_table.verticalHeader().setVisible(False)

        header = self.suppliers_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)

        # إضافة خاصية التكيف التلقائي مطابقة للفواتير والعملاء مع مقاسات مخصصة
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تعيين مقاسات ثابتة لأعمدة محددة مع الحفاظ على التكيف التلقائي للباقي
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # عمود ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # عمود اسم المورد
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # عمود العنوان
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # عمود البريد الإلكتروني
        self.suppliers_table.setColumnWidth(0, 120)  # ID - 120 بكسل
        self.suppliers_table.setColumnWidth(1, 300)  # اسم المورد - 300 بكسل
        self.suppliers_table.setColumnWidth(2, 300)  # العنوان - 300 بكسل
        self.suppliers_table.setColumnWidth(3, 250)  # البريد الإلكتروني - 250 بكسل

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.suppliers_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.suppliers_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق التصميم والتفاعل مطابق للعملاء
        self.apply_table_style()
        self.add_watermark_to_table()
        self.setup_table_interactions()



    def filter_suppliers(self):
        """تصفية الموردين بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = self.current_filter_value

            # بناء الاستعلام
            query = self.session.query(Supplier)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Supplier.name.like(f"%{search_text}%") |
                    Supplier.phone.like(f"%{search_text}%") |
                    Supplier.email.like(f"%{search_text}%") |
                    Supplier.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Supplier.balance > 0)
            elif status == "normal":
                query = query.filter(Supplier.balance == 0)
            elif status == "debtor":
                query = query.filter(Supplier.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            suppliers = query.order_by(Supplier.id.asc()).all()

            # تحديث الجدول والملخص
            self.populate_table(suppliers)
            self.update_summary(suppliers)

        except Exception as e:
            print(f"حدث خطأ أثناء تصفية البيانات: {str(e)}")

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول مطابق للفواتير"""
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));

                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_watermark_to_table(self):
        """إضافة علامة مائية للجدول مطابقة للفواتير"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))

            # رسم النص الرئيسي بحجم خط كبير جداً (180) في منتصف الارتفاع
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)

            # حساب منتصف الارتفاع
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")

            painter.restore()

        # تطبيق العلامة المائية
        original_paint = self.suppliers_table.paintEvent
        def new_paint_event(event):
            original_paint(event)
            painter = QPainter(self.suppliers_table.viewport())
            paint_watermark(painter, self.suppliers_table.viewport().rect())
            painter.end()

        self.suppliers_table.paintEvent = new_paint_event

    def setup_table_interactions(self):
        """إعداد التفاعلات مع الجدول مطابق للفواتير"""
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.suppliers_table.cellDoubleClicked.connect(self.edit_supplier)

        # إلغاء التحديد عند النقر على منطقة فارغة
        def mousePressEvent(event):
            item = self.suppliers_table.itemAt(event.pos())
            if item is None:
                self.suppliers_table.clearSelection()
            QTableWidget.mousePressEvent(self.suppliers_table, event)

        self.suppliers_table.mousePressEvent = mousePressEvent

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.suppliers_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.suppliers_table, event)

        self.suppliers_table.wheelEvent = wheelEvent

    def on_selection_changed(self):
        """معالج تغيير التحديد في الجدول مطابق للفواتير"""
        selected_items = self.suppliers_table.selectedItems()
        has_selection = len(selected_items) > 0

        # تفعيل/تعطيل الأزرار حسب التحديد
        if hasattr(self, 'edit_button'):
            self.edit_button.setEnabled(has_selection)
        if hasattr(self, 'delete_button'):
            self.delete_button.setEnabled(has_selection)
        if hasattr(self, 'view_button'):
            self.view_button.setEnabled(has_selection)
        if hasattr(self, 'add_payment_button'):
            self.add_payment_button.setEnabled(has_selection)

    def edit_supplier(self):
        """تعديل بيانات مورد"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                QMessageBox.warning(self, "تحذير", error)
                return

            # الحصول على بيانات المورد من قاعدة البيانات
            supplier = self.session.query(Supplier).get(supplier_id)
            if supplier:
                # فتح نافذة التعديل (نفس نافذة الإضافة مع تحميل البيانات)
                dialog = AddSupplierDialog(self.session, self, supplier)
                if dialog.exec_() == QDialog.Accepted:
                    self.refresh_data()  # تحديث الجدول بعد التعديل
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على المورد المحدد")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تعديل المورد: {str(e)}")

    def get_selected_supplier_id(self):
        """استخراج معرف المورد المحدد من الجدول مطابق للفواتير"""
        try:
            selected_row = self.suppliers_table.currentRow()
            if selected_row < 0:
                return None, "الرجاء اختيار مورد من القائمة"

            if not self.suppliers_table.item(selected_row, 0):
                return None, "الرجاء اختيار مورد صالح من القائمة"

            # استخراج الرقم من النص (إزالة # والأيقونات)
            id_text = self.suppliers_table.item(selected_row, 0).text()
            numbers = re.findall(r'\d+', id_text)
            if not numbers:
                return None, "لا يمكن استخراج رقم المورد"

            supplier_id = int(numbers[0])
            return supplier_id, None

        except Exception as e:
            return None, f"خطأ في استخراج معرف المورد: {str(e)}"

    def refresh_data(self):
        """تحديث بيانات الجدول مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True

            # تعطيل زر التحديث مؤقتاً
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("🔄 جاري التحديث...")

            # جلب جميع الموردين من قاعدة البيانات (من الأقدم للأحدث)
            suppliers = self.session.query(Supplier).order_by(Supplier.id.asc()).all()
            self.populate_table(suppliers)
            self.update_summary(suppliers)

        except Exception as e:
            print(f"حدث خطأ أثناء تحديث البيانات: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

    def update_summary(self, suppliers):
        """تحديث ملخص الموردين مطابق للفواتير"""
        try:
            # حساب الإحصائيات
            total_suppliers = len(suppliers)
            active_suppliers = len([s for s in suppliers if (s.balance or 0) > 0])
            debtor_suppliers = len([s for s in suppliers if (s.balance or 0) < 0])
            normal_suppliers = len([s for s in suppliers if (s.balance or 0) == 0])

            # حساب المبالغ
            total_positive = sum(s.balance for s in suppliers if (s.balance or 0) > 0)
            total_negative = sum(abs(s.balance) for s in suppliers if (s.balance or 0) < 0)
            net_balance = sum(s.balance or 0 for s in suppliers)

            # تحديث النصوص (إذا كان هناك عنصر ملخص)
            if hasattr(self, 'summary_label'):
                summary_text = (f"إجمالي الموردين: {total_suppliers} | "
                              f"نشط: {active_suppliers} | "
                              f"مدين: {debtor_suppliers} | "
                              f"عادي: {normal_suppliers}")
                self.summary_label.setText(summary_text)

            if hasattr(self, 'balance_label'):
                balance_text = (f"الأرصدة الموجبة: {format_currency(total_positive)} | "
                              f"الأرصدة السالبة: {format_currency(total_negative)} | "
                              f"صافي الرصيد: {format_currency(net_balance)}")
                self.balance_label.setText(balance_text)



        except Exception as e:
            print(f"خطأ في تحديث ملخص الموردين: {str(e)}")

    def populate_table(self, suppliers):
        """ملء الجدول بالبيانات مطابق للفواتير"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.suppliers_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.suppliers_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن مطابق للفواتير
            for row, supplier in enumerate(suppliers):
                try:
                    self.suppliers_table.insertRow(row)

                    # 1. الرقم التسلسلي مع أيقونة حسب الرصيد مطابق للعملاء
                    balance_value = supplier.balance or 0
                    if balance_value > 0:
                        id_icon = "💰"
                    elif balance_value < 0:
                        id_icon = "🔴"
                    else:
                        id_icon = "🔢"

                    # إنشاء عنصر ID مع لون أسود للرقم مطابق للعملاء
                    id_item = QTableWidgetItem(f"{id_icon} {supplier.id}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
                    self.suppliers_table.setItem(row, 0, id_item)

                    # دالة مساعدة لإنشاء العناصر مطابق للعملاء
                    def create_item(icon, text, default="No Data"):
                        display_text = text if text and text.strip() else default
                        item = QTableWidgetItem(f"{icon} {display_text}")
                        item.setTextAlignment(Qt.AlignCenter)
                        if display_text == default:
                            item.setForeground(QColor("#ef4444"))
                        return item

                    self.suppliers_table.setItem(row, 1, create_item("🚛", supplier.name))
                    self.suppliers_table.setItem(row, 2, create_item("🏠", supplier.address))
                    self.suppliers_table.setItem(row, 3, create_item("📧", supplier.email))

                    self.suppliers_table.setItem(row, 4, create_item("📱", supplier.phone))

                    # 6. الرصيد مع ألوان حسب القيمة مطابق للعملاء
                    balance_value = supplier.balance or 0
                    balance_text = format_currency(balance_value)
                    if balance_value > 0:
                        balance_item = QTableWidgetItem(f"💰 {balance_text}")
                        balance_item.setForeground(QColor("#059669"))  # أخضر للموجب
                    elif balance_value < 0:
                        balance_item = QTableWidgetItem(f"💸 {balance_text}")
                        balance_item.setForeground(QColor("#dc2626"))  # أحمر للسالب
                    else:
                        balance_item = QTableWidgetItem(f"💵 {balance_text}")
                        balance_item.setForeground(QColor("#000000"))  # أسود للصفر
                    balance_item.setTextAlignment(Qt.AlignCenter)
                    self.suppliers_table.setItem(row, 5, balance_item)

                    # 7. حالة المورد مطابق للعملاء
                    status_item = QTableWidgetItem(self.get_supplier_status(balance_value))
                    status_item.setTextAlignment(Qt.AlignCenter)
                    self.suppliers_table.setItem(row, 6, status_item)

                    # الملاحظات والتاريخ مطابق للعملاء
                    notes_text = supplier.notes if hasattr(supplier, 'notes') and supplier.notes and supplier.notes.strip() else None
                    date_text = supplier.created_at.strftime("%Y-%m-%d") if hasattr(supplier, 'created_at') and supplier.created_at else None

                    self.suppliers_table.setItem(row, 7, create_item("📋", notes_text))
                    self.suppliers_table.setItem(row, 8, create_item("🗓️", date_text))

                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تفعيل تحديث الجدول
            self.suppliers_table.setUpdatesEnabled(True)

        except Exception as e:
            # إعادة تفعيل تحديث الجدول في حالة الخطأ
            self.suppliers_table.setUpdatesEnabled(True)
            print(f"حدث خطأ أثناء تحديث جدول الموردين: {str(e)}")

    def get_supplier_status(self, balance):
        """تحديد حالة المورد بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"



    def create_action_buttons(self):
        """إنشاء إطار الأزرار مطابق للفواتير"""

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للفواتير
        self.buttons_frame = QFrame()
        self.buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار النظيفة والمرتبة مطابق للفواتير

        # زر الإضافة
        self.add_button = QPushButton("➕ إضافة مورد")
        self.style_advanced_button(self.add_button, 'emerald')
        self.add_button.clicked.connect(self.add_supplier)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')
        self.edit_button.clicked.connect(self.edit_supplier)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الحذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')
        self.delete_button.clicked.connect(self.delete_supplier)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر عرض التفاصيل
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'indigo')
        self.view_button.clicked.connect(self.view_supplier)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر تعديل المبلغ (إضافة أو تقليل)
        self.add_payment_button = QPushButton("💰 تعديل المبلغ")
        self.style_advanced_button(self.add_payment_button, 'orange')
        self.add_payment_button.clicked.connect(self.add_payment)
        self.add_payment_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التصدير
        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير مطابقة للفواتير
        export_menu = QMenu(self)
        export_menu.setStyleSheet("""
            QMenu {
                background: #ffffff;
                border: 2px solid #3b82f6;
                border-radius: 8px;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background: #3b82f6;
                color: white;
            }
        """)

        excel_action = QAction("📊 تصدير Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        self.export_button.setMenu(export_menu)

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)



        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.add_payment_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)

        # تعيين التخطيط للإطار السفلي
        self.buttons_frame.setLayout(bottom_container)

        # تعطيل الأزرار التي تحتاج تحديد عنصر
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.view_button.setEnabled(False)
        self.add_payment_button.setEnabled(False)

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception:
            pass

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة بدون مشاكل مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # تخطيط أفقي للإطار مع التوسيط العمودي
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(8, 0, 8, 0)  # إزالة الهوامش العمودية
        filter_layout.setSpacing(8)
        filter_layout.setAlignment(Qt.AlignVCenter)  # توسيط عمودي للعناصر

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        self.status_filter_frame.setLayout(filter_layout)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة العناصر
        filter_options = [
            ("جميع الحالات", None),
            ("🟢 نشط (رصيد موجب)", "active"),
            ("🟡 عادي (رصيد صفر)", "normal"),
            ("🔴 مدين (رصيد سالب)", "debtor")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_suppliers()

    # ==================== دوال معالجة أحداث الأزرار ====================

    def add_supplier(self):
        """إضافة مورد جديد"""
        try:
            dialog = AddSupplierDialog(self.session, self)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()  # تحديث الجدول بعد الإضافة
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إضافة المورد: {str(e)}")

    def get_selected_supplier_id(self):
        """الحصول على معرف المورد المحدد"""
        try:
            current_row = self.suppliers_table.currentRow()
            if current_row >= 0:
                # استخراج الرقم من النص (إزالة الأيقونات)
                id_text = self.suppliers_table.item(current_row, 0).text()
                # إزالة الأيقونات والمسافات والحصول على الرقم فقط
                supplier_id = int(''.join(filter(str.isdigit, id_text)))
                return supplier_id, None
            else:
                return None, "يرجى اختيار مورد من القائمة"
        except Exception as e:
            return None, f"خطأ في تحديد المورد: {str(e)}"

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مشابهة لنوافذ البرنامج"""
        dialog = WarningDialog(self, message)
        dialog.exec_()

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("✅ نجح")
        msg.setText(message)
        msg.exec_()

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("❌ خطأ")
        msg.setText(message)
        msg.exec_()

    def delete_supplier(self):
        """حذف مورد مع نافذة تأكيد متطورة"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if supplier_id:
                # الحصول على بيانات المورد
                supplier = self.session.query(Supplier).filter(Supplier.id == supplier_id).first()
                if supplier:
                    # إنشاء نافذة حذف مشابهة لنافذة العملاء
                    dialog = DeleteSupplierDialog(self, supplier)
                    if dialog.exec_() == QDialog.Accepted:
                        try:
                            # حذف المورد من قاعدة البيانات
                            self.session.delete(supplier)
                            self.session.commit()

                            # إظهار رسالة نجاح متطورة
                            self.show_success_message(f"تم حذف المورد '{supplier.name}' بنجاح")

                            # تحديث الجدول
                            self.refresh_data()

                        except Exception as e:
                            self.session.rollback()
                            self.show_error_message(f"فشل في حذف المورد: {str(e)}")
                else:
                    self.show_error_message("لم يتم العثور على المورد المحدد")
            else:
                self.show_warning_message("يرجى اختيار مورد للحذف")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف المورد: {str(e)}")

    def view_supplier(self):
        """عرض تفاصيل المورد في نافذة متطورة"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد لعرض تفاصيله")
                return

            print(f"عرض تفاصيل المورد رقم: {supplier_id}")
            supplier = self.session.query(Supplier).filter(Supplier.id == supplier_id).first()
            if supplier:
                # إنشاء نافذة المعلومات المتطورة
                info_dialog = SupplierInfoDialog(self, supplier)
                info_dialog.exec_()
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على المورد")
        except Exception as e:
            print(f"خطأ في عرض تفاصيل المورد: {str(e)}")
            self.show_error_message(f"حدث خطأ في عرض تفاصيل المورد: {str(e)}")

    def add_payment(self):
        """تعديل رصيد المورد - إضافة أو تقليل المبلغ"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                show_error_message("تحذير", error)
                return

            supplier = self.session.query(Supplier).get(supplier_id)
            if not supplier:
                show_error_message("خطأ", "لم يتم العثور على المورد")
                return

            # فتح نافذة تعديل المبلغ
            dialog = EditSupplierAmountDialog(self, supplier, self.session)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ: {str(e)}")

    def export_to_excel(self):
        """تصدير إلى Excel - دالة مؤقتة"""
        print("📊 تصدير إلى Excel")
        # TODO: إضافة تصدير Excel

    def export_to_csv(self):
        """تصدير إلى CSV - دالة مؤقتة"""
        print("📄 تصدير إلى CSV")
        # TODO: إضافة تصدير CSV

    def show_statistics(self):
        """عرض الإحصائيات - دالة مؤقتة"""
        print("📊 عرض الإحصائيات")
        # TODO: إضافة نافذة الإحصائيات

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة"""
        try:
            print("🔧 بدء تهيئة حالة أزرار الموردين...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_button, "➕ إضافة مورد"),
                (self.edit_button, "✏️ تعديل"),
                (self.delete_button, "🗑️ حذف"),
                (self.refresh_button, "🔄 تحديث"),
                (self.view_button, "👁️ عرض التفاصيل"),
                (self.add_payment_button, "💰 تعديل المبلغ"),
                (self.export_button, "📤 تصدير"),
                (self.statistics_button, "📊 الإحصائيات")
            ]

            for button, name in buttons:
                if button:
                    button.setEnabled(True)
                    # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                    current_style = button.styleSheet()
                    # إزالة أي opacity موجودة
                    clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                    # إضافة opacity كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                    button.setStyleSheet(new_style)
                    button.show()
                    print(f"🟢 تم تفعيل الزر: {name}")

            print("✅ تم تهيئة حالة أزرار الموردين بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تهيئة حالة أزرار الموردين: {str(e)}")


class AddSupplierDialog(QDialog):
    """نافذة إضافة أو تعديل مورد مع نظام تعدد الأرقام - مطابقة تماماً للعملاء"""

    def __init__(self, session, parent=None, supplier=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent  # حفظ مرجع للوالد
        self.supplier = supplier  # المورد للتعديل (None للإضافة)
        self.is_edit_mode = supplier is not None  # تحديد وضع التعديل

        # سيتم تحديد العنوان في setup_ui
        self.setModal(True)
        self.resize(650, 650)  # جعل النافذة مربعة
        self.setup_ui()

        # تحميل البيانات في وضع التعديل
        if self.is_edit_mode:
            self.load_supplier_data()

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار - نسخة مبسطة لنافذة الحوار"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم بسيط كبديل
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def setup_ui(self):
        """إعداد واجهة النافذة - مطابقة تماماً للعملاء"""
        # استخدام شريط العنوان الطبيعي للنظام مع النص في المنتصف
        if self.is_edit_mode:
            self.setWindowTitle("✏️ تعديل - نظام إدارة الموردين المتطور والشامل")
        else:
            self.setWindowTitle("🚛 إضافة - نظام إدارة الموردين المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة مطابقة للنافذة الرئيسية
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # تخطيط المحتوى الرئيسي للنافذة - مطابق للعملاء
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(18)

        # إضافة عنوان النافذة الداخلي - يتغير حسب الوضع
        if self.is_edit_mode:
            title_text = "تعديل بيانات المورد"
            title_icon = "✏️"
        else:
            title_text = "إضافة مورد جديد"
            title_icon = "🏭"

        title_label = QLabel(f"{title_icon} {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 18px 25px;
                margin: 8px 5px;
                font-weight: bold;
                font-size: 18px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4),
                           0 3px 12px rgba(37, 99, 235, 0.3);
                min-height: 50px;
                max-height: 50px;
            }
        """)
        layout.addWidget(title_label)

        # نموذج البيانات مع تصميم محسن - مطابق للعملاء
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)
        form_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignTop)
        form_layout.setHorizontalSpacing(15)
        form_layout.setVerticalSpacing(12)

        # إنشاء دالة لتصميم النصوص مع عرض مقلل - مطابقة للعملاء
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 120px;
                    max-width: 120px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # اسم المورد (مطلوب) - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المورد الكامل هنا... (مطلوب)")
        self.name_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("اسم المورد", "🚛", True), self.name_edit)

        # العنوان - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.address_edit = QLineEdit()
        self.address_edit.setPlaceholderText("أدخل العنوان الكامل للمورد (الشارع، المدينة، المحافظة)...")
        self.address_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("العنوان", "🏠"), self.address_edit)

        # البريد الإلكتروني - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("أدخل البريد الإلكتروني الكامل (<EMAIL>)...")
        self.email_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("البريد الإلكتروني", "📧"), self.email_edit)

        # أرقام الهاتف (نظام تعدد الأرقام) - مطابق للعملاء
        phone_container = QVBoxLayout()

        # الهاتف الأساسي - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف الأساسي (مثال: 01234567890)...")
        self.phone_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        phone_container.addWidget(self.phone_edit)

        # أرقام إضافية
        self.additional_phones = []
        self.phone_widgets = []

        # زر إضافة رقم هاتف مع ارتفاع مقلل ومنزل للأسفل - مطابق للعملاء
        add_phone_btn = QPushButton("➕ إضافة رقم")
        self.style_advanced_button(add_phone_btn, 'info')
        add_phone_btn.clicked.connect(self.add_phone_field)

        # تقليل ارتفاع الزر وإنزاله للأسفل - مطابق للعملاء
        add_phone_btn.setStyleSheet(add_phone_btn.styleSheet() + """
            QPushButton {
                margin-top: 15px;
                margin-bottom: 8px;
                margin-left: 5px;
                margin-right: 5px;
                max-height: 35px;
                min-height: 35px;
                padding: 6px 12px;
                font-size: 14px;
            }
        """)

        phone_container.addWidget(add_phone_btn)

        form_layout.addRow(create_styled_label("أرقام الهاتف", "📱"), phone_container)

        # الرصيد الابتدائي - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.balance_spinbox = QDoubleSpinBox()
        self.balance_spinbox.setRange(-999999999, 999999999)
        self.balance_spinbox.setDecimals(0)  # إزالة الأرقام العشرية
        self.balance_spinbox.setValue(0)
        self.balance_spinbox.setSuffix(" جنيه")
        self.balance_spinbox.setButtonSymbols(QDoubleSpinBox.NoButtons)  # إزالة الأسهم
        self.balance_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 18px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("الرصيد الابتدائي", "💰"), self.balance_spinbox)

        # الملاحظات - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات أو تفاصيل إضافية عن المورد هنا...")
        self.notes_edit.setMaximumHeight(100)  # ارتفاع أكبر قليلاً
        self.notes_edit.setMinimumWidth(350)   # عرض أكبر
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("الملاحظات", "📝"), self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار التحكم - مطابقة للعملاء
        buttons_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء - يتغير النص حسب الوضع
        if self.is_edit_mode:
            save_button = QPushButton("✏️ تحديث")
        else:
            save_button = QPushButton("💾 حفظ")
        self.parent_widget.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.save_supplier)

        # زر الإلغاء - أحمر للخطر - مطابق للعملاء
        cancel_button = QPushButton("❌ إلغاء")
        self.parent_widget.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        layout.addLayout(buttons_layout)

    def add_phone_field(self):
        """إضافة حقل هاتف جديد مع زر حذف"""
        try:
            # إنشاء حاوي أفقي للحقل والزر
            phone_container = QHBoxLayout()
            phone_container.setSpacing(8)

            # إنشاء حقل الهاتف الجديد
            phone_edit = QLineEdit()
            phone_edit.setPlaceholderText("رقم هاتف إضافي...")
            phone_edit.setStyleSheet(self.phone_edit.styleSheet())

            # إنشاء زر الحذف
            remove_btn = QPushButton("🗑️")
            self.parent_widget.style_advanced_button(remove_btn, 'danger')
            remove_btn.setMaximumWidth(40)
            remove_btn.setMaximumHeight(35)
            remove_btn.clicked.connect(lambda: self.remove_phone_field(phone_container, phone_edit))

            # إضافة العناصر للحاوي مع ترتيب معكوس - الحقل أولاً ثم الزر
            phone_container.addWidget(phone_edit)  # الحقل أولاً في اليمين
            phone_container.addWidget(remove_btn)  # الزر ثانياً في اليسار
            phone_container.addStretch()  # إضافة مساحة مرنة في النهاية

            # إضافة إلى القائمة
            self.additional_phones.append(phone_edit)
            self.phone_widgets.append(phone_container)

            # إضافة إلى الواجهة بطريقة محسنة
            self.add_phone_to_form(phone_container)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في إضافة حقل الهاتف: {str(e)}")

    def add_phone_to_form(self, phone_container):
        """إضافة حقل الهاتف إلى النموذج بطريقة محسنة"""
        try:
            # إنشاء widget للحاوي
            phone_widget = QWidget()
            phone_widget.setLayout(phone_container)

            # الحصول على النموذج الرئيسي - البحث عن form_layout
            main_layout = self.layout()
            form_layout = None

            # البحث عن QFormLayout في التخطيط الرئيسي
            for i in range(main_layout.count()):
                item = main_layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    if hasattr(widget, 'layout') and widget.layout():
                        layout = widget.layout()
                        if isinstance(layout, QFormLayout):
                            form_layout = layout
                            break

            if form_layout:
                # إضافة الحقل الجديد بعد آخر حقل هاتف
                row_count = form_layout.rowCount()
                form_layout.insertRow(row_count - 2, "", phone_widget)  # إدراج قبل الملاحظات والرصيد
            else:
                print("لم يتم العثور على form_layout")

        except Exception as e:
            print(f"خطأ في إضافة حقل الهاتف للنموذج: {str(e)}")

    def remove_phone_field(self, phone_container, phone_edit):
        """حذف حقل هاتف"""
        try:
            # إزالة من القوائم
            if phone_edit in self.additional_phones:
                self.additional_phones.remove(phone_edit)
            if phone_container in self.phone_widgets:
                self.phone_widgets.remove(phone_container)

            # إزالة من الواجهة
            widget = phone_container.parent()
            if widget:
                widget.setParent(None)
                widget.deleteLater()

        except Exception as e:
            print(f"خطأ في حذف حقل الهاتف: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان الطبيعي مع التدرجات والألوان الجديدة المتطورة"""
        try:
            # إنشاء أيقونة مخصصة متطورة مع تدرجات جديدة
            pixmap = QPixmap(48, 48)  # حجم أكبر للوضوح
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setRenderHint(QPainter.SmoothPixmapTransform)

            # إنشاء تدرج متطور للأيقونة - مطابق للعملاء
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق مطابق للعملاء
            gradient.setColorAt(0.7, QColor(37, 99, 235))  # أزرق داكن
            gradient.setColorAt(1, QColor(29, 78, 216))  # أزرق عميق

            # رسم دائرة متدرجة
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)

            # رسم رمز المورد - يتغير حسب الوضع
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))

            # اختيار الرمز حسب الوضع
            icon_text = "✏️" if self.is_edit_mode else "🚛"
            painter.drawText(12, 30, icon_text)

            painter.end()

            # تعيين الأيقونة
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم متطور على شريط العنوان
            self.apply_advanced_title_bar_styling()

            # توسيط النص في شريط العنوان
            self.center_title_text()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)

    def center_title_text(self):
        """تحسين وضع النص في منتصف شريط العنوان"""
        try:
            # إضافة مسافات لتوسيط النص بصرياً
            # تحديد العنوان حسب الوضع
            if self.is_edit_mode:
                original_title = "✏️ تعديل - نظام إدارة الموردين المتطور والشامل"
            else:
                original_title = "🚛 إضافة - نظام إدارة الموردين المتطور والشامل"

            # حساب المسافات المطلوبة للتوسيط
            padding_spaces = "    "  # مسافات إضافية للتوسيط
            centered_title = f"{padding_spaces}{original_title}{padding_spaces}"

            # تحديث العنوان مع التوسيط
            self.setWindowTitle(centered_title)

        except Exception as e:
            print(f"تحذير: فشل في توسيط النص: {e}")

    def save_supplier(self):
        """حفظ المورد الجديد أو تحديث المورد الموجود مع نظام تعدد الأرقام"""
        try:
            # التحقق من صحة البيانات
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المورد")
                self.name_edit.setFocus()
                return

            # جمع أرقام الهاتف
            phone_numbers = []

            # الرقم الأساسي
            main_phone = self.phone_edit.text().strip()
            if main_phone:
                phone_numbers.append(main_phone)

            # الأرقام الإضافية
            for phone_edit in self.additional_phones:
                additional_phone = phone_edit.text().strip()
                if additional_phone:
                    phone_numbers.append(additional_phone)

            # تحويل قائمة الأرقام إلى نص مفصول بفواصل
            phone_string = ", ".join(phone_numbers) if phone_numbers else None

            if self.is_edit_mode:
                # تحديث المورد الموجود
                self.supplier.name = name
                self.supplier.address = self.address_edit.text().strip() or None
                self.supplier.email = self.email_edit.text().strip() or None
                self.supplier.phone = phone_string
                self.supplier.balance = self.balance_spinbox.value()
                self.supplier.notes = self.notes_edit.toPlainText().strip() or None

                # حفظ التغييرات
                self.session.commit()

                # رسالة نجاح التحديث
                phone_info = f"\nأرقام الهاتف: {phone_string}" if phone_string else "\nلم يتم إدخال أرقام هاتف"
                QMessageBox.information(
                    self,
                    "تم التحديث",
                    f"تم تحديث بيانات المورد '{name}' بنجاح!{phone_info}"
                )
            else:
                # إنشاء مورد جديد
                new_supplier = Supplier(
                    name=name,
                    address=self.address_edit.text().strip() or None,
                    email=self.email_edit.text().strip() or None,
                    phone=phone_string,  # حفظ جميع الأرقام
                    balance=self.balance_spinbox.value(),
                    notes=self.notes_edit.toPlainText().strip() or None
                )

                # حفظ في قاعدة البيانات
                self.session.add(new_supplier)
                self.session.commit()

                # رسالة نجاح الإضافة
                phone_info = f"\nأرقام الهاتف: {phone_string}" if phone_string else "\nلم يتم إدخال أرقام هاتف"
                QMessageBox.information(
                    self,
                    "نجح",
                    f"تم إضافة المورد '{name}' بنجاح!{phone_info}"
                )

            self.accept()

        except Exception as e:
            # التراجع عن التغييرات في حالة الخطأ
            self.session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ المورد: {str(e)}")

    def load_supplier_data(self):
        """تحميل بيانات المورد في وضع التعديل"""
        if not self.supplier:
            return

        try:
            # تحميل البيانات الأساسية
            self.name_edit.setText(self.supplier.name or "")
            self.address_edit.setText(self.supplier.address or "")
            self.email_edit.setText(self.supplier.email or "")
            self.balance_spinbox.setValue(self.supplier.balance or 0.0)
            self.notes_edit.setPlainText(self.supplier.notes or "")

            # تحميل أرقام الهاتف
            if self.supplier.phone:
                # تقسيم أرقام الهاتف المفصولة بفواصل
                phone_numbers = [phone.strip() for phone in self.supplier.phone.split(",") if phone.strip()]

                if phone_numbers:
                    # الرقم الأول في الحقل الأساسي
                    self.phone_edit.setText(phone_numbers[0])

                    # الأرقام الإضافية
                    for i, phone in enumerate(phone_numbers[1:], 1):
                        if i <= len(self.additional_phones):
                            # استخدام الحقول الموجودة
                            self.additional_phones[i-1].setText(phone)
                        else:
                            # إضافة حقول جديدة للأرقام الإضافية
                            self.add_phone_field()
                            if self.additional_phones:
                                self.additional_phones[-1].setText(phone)

        except Exception as e:
            print(f"خطأ في تحميل بيانات المورد: {e}")


class DeleteSupplierDialog(QDialog):
    """نافذة حذف المورد مشابهة لنافذة حذف العميل"""

    def __init__(self, parent=None, supplier=None):
        super().__init__(parent)
        self.supplier = supplier
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف العميل"""
        self.setWindowTitle("🏢 حذف - نظام إدارة الموردين المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("🏢 حذف المورد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات المورد مضغوطة
        if self.supplier:
            info_text = f"🏢 {self.supplier.name[:15]}{'...' if len(self.supplier.name) > 15 else ''}"
            if self.supplier.balance:
                info_text += f" | 💰 {self.supplier.balance:.0f} ج"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("🏢 حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "🏢")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class SupplierInfoDialog(QDialog):
    """
    نافذة عرض معلومات المورد التفصيلية - مطابقة للنموذج المرجعي

    هذه النافذة مطابقة للنموذج المرجعي في قسم العملاء
    المميزات:
    - تصميم موحد ومتسق مع حواف مربعة
    - ألوان واضحة ومتباينة للبيانات
    - تخطيط منظم ومرن
    - أزرار وظيفية متطورة
    - أيقونات محسنة ومتطورة
    """

    def __init__(self, parent=None, supplier=None):
        super().__init__(parent)
        self.supplier = supplier
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة نافذة المعلومات المرجعية"""
        # ═══════════════════════════════════════════════════════════════
        # إعدادات النافذة الأساسية
        # ═══════════════════════════════════════════════════════════════
        self.setWindowTitle("🏢📋 معلومات المورد - نظام إدارة الموردين المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setWindowIcon(self.create_window_icon())
        self.customize_title_bar()
        self.setModal(True)
        self.resize(850, 780)  # حجم محسن للعرض الأمثل

        # ═══════════════════════════════════════════════════════════════
        # تصميم النافذة والخلفية المرجعية
        # ═══════════════════════════════════════════════════════════════
        self.setStyleSheet(self.get_reference_styling())

        # ═══════════════════════════════════════════════════════════════
        # التخطيط الرئيسي المرجعي
        # ═══════════════════════════════════════════════════════════════
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        self.setGraphicsEffect(self.create_shadow_effect())

        # عنوان النافذة الداخلي المحسن
        title_container = QFrame()
        title_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(8, 1, 8, 5)
        title_layout.setSpacing(5)

        # العنوان المضغوط مع اسم المورد - في المنتصف مع خط أكبر
        title_text = f"🏢 معلومات المورد: {self.supplier.name if self.supplier and self.supplier.name else 'غير محدد'}"
        main_title = QLabel(title_text)
        main_title.setAlignment(Qt.AlignCenter | Qt.AlignVCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: 900;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 3px 15px 10px 15px;
                background: transparent;
                border: none;
                line-height: 1.2;
            }
        """)
        title_layout.addWidget(main_title, 1)

        layout.addWidget(title_container)

        # إنشاء منطقة التمرير المحسنة للمعلومات - مطابقة تماماً للنموذج المرجعي
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(71, 85, 105, 0.4),
                    stop:1 rgba(100, 116, 139, 0.3));
                width: 16px;
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6),
                    stop:0.5 rgba(139, 92, 246, 0.5),
                    stop:1 rgba(34, 197, 94, 0.4));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 7px;
                min-height: 30px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(34, 197, 94, 0.6));
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(37, 99, 235, 0.9),
                    stop:0.5 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(21, 128, 61, 0.7));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: transparent;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # محتوى المعلومات المحسن - مطابق تماماً للنموذج المرجعي
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.1),
                    stop:0.2 rgba(30, 41, 59, 0.08),
                    stop:0.5 rgba(51, 65, 85, 0.06),
                    stop:0.8 rgba(71, 85, 105, 0.08),
                    stop:1 rgba(100, 116, 139, 0.1));
                border-radius: 12px;
                padding: 10px;
            }
        """)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(25)  # زيادة المسافة بين الأقسام لاستغلال المساحة الإضافية

        # إضافة معلومات المورد
        self.add_supplier_info(info_layout)

        scroll_area.setWidget(info_widget)
        layout.addWidget(scroll_area)

        # أزرار التحكم
        self.create_control_buttons(layout)

        # تطبيق تصميم شريط العنوان
        self.apply_advanced_title_bar_styling()

    def add_supplier_info(self, layout):
        """إضافة معلومات المورد إلى التخطيط - مطابق تماماً للنموذج المرجعي"""
        if not self.supplier:
            return

        # قسم المعلومات الأساسية والشخصية
        basic_info = [
            ("🔢 المعرف الفريد", f"#{str(self.supplier.id).zfill(8)}"),
            ("📧 البريد الإلكتروني", self.supplier.email or "غير محدد"),
            ("📍 العنوان الكامل", self.supplier.address or "غير محدد"),
            ("🏷️ حالة المورد", "نشط ✅" if self.supplier.balance is not None else "غير نشط ❌"),
            ("📊 مستوى البيانات", self.get_data_completeness())
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية والشخصية", basic_info)

        # قسم المعلومات المالية المحسن
        balance_color = self.get_balance_color()
        balance_text = f"{self.supplier.balance:.0f} جنيه" if self.supplier.balance else "0 جنيه"
        self.add_info_section(layout, "💰 المعلومات المالية", [
            ("💵 الرصيد الحالي", f"{balance_color} {balance_text}"),
            ("📊 حالة الحساب", self.get_account_status()),
            ("💳 تصنيف المورد", self.get_supplier_type()),
            ("📈 مستوى النشاط", self.get_activity_level()),
            ("⚖️ تقييم الائتمان", self.get_credit_rating())
        ])

        # قسم معلومات الاتصال المحسن
        self.add_info_section(layout, "📞 معلومات الاتصال", [
            ("📱 الهاتف الرئيسي", self.supplier.phone or "غير محدد"),
            ("📞 أرقام إضافية", self.get_additional_phones()),
            ("📧 حالة البريد", self.get_email_status()),
            ("📲 طرق التواصل", self.get_contact_methods())
        ])

        # قسم معلومات التاريخ والإحصائيات
        self.add_info_section(layout, "📅 معلومات التاريخ والإحصائيات", [
            ("📅 تاريخ الإضافة", self.get_creation_date()),
            ("⏰ آخر تحديث", self.get_last_update()),
            ("📈 مدة التعامل", self.get_membership_duration()),
            ("📊 عدد المعاملات", self.get_transactions_count()),
            ("💼 إجمالي التعاملات", self.get_total_transactions())
        ])

        # قسم الملاحظات والتفاصيل الإضافية
        self.add_info_section(layout, "📝 ملاحظات وتفاصيل إضافية", [
            ("📝 الملاحظات", self.supplier.notes or "لا توجد ملاحظات"),
            ("🔍 معلومات إضافية", self.get_additional_info()),
            ("📋 ملخص الحساب", self.get_account_summary())
        ])

    def add_info_section(self, layout, title, items):
        """إضافة قسم معلومات محسن مع تصميم متطور - مطابق للنموذج المرجعي"""
        # إطار القسم الرئيسي
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(241, 245, 249, 0.10),
                    stop:1 rgba(226, 232, 240, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 15px;
                margin: 8px 0px;
                padding: 0px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15, 15, 15, 15)
        section_layout.setSpacing(12)

        # عنوان القسم المحسن
        section_title = QLabel(title)
        section_title.setAlignment(Qt.AlignCenter)
        section_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(34, 197, 94, 0.2));
                border: 2px solid rgba(255, 255, 255, 0.25);
                border-radius: 12px;
                padding: 12px 20px;
                margin-bottom: 10px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        section_layout.addWidget(section_title)

        # عناصر القسم المحسنة
        for i, (label, value) in enumerate(items):
            item_widget = QWidget()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(12, 8, 12, 8)
            item_layout.setSpacing(15)

            # خلفية متدرجة للعناصر
            item_widget.setStyleSheet(f"""
                QWidget {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, {0.03 + (i % 2) * 0.02}),
                        stop:0.5 rgba(248, 250, 252, {0.05 + (i % 2) * 0.02}),
                        stop:1 rgba(241, 245, 249, {0.03 + (i % 2) * 0.02}));
                    border: 1px solid rgba(255, 255, 255, 0.12);
                    border-radius: 10px;
                    margin: 2px 0px;
                }}
                QWidget:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.08),
                        stop:0.5 rgba(139, 92, 246, 0.06),
                        stop:1 rgba(34, 197, 94, 0.05));
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }}
            """)

            # التسمية المحسنة - نصوص أوضح
            label_widget = QLabel(label)
            label_widget.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: 800;
                    min-width: 180px;
                    max-width: 180px;
                    padding: 12px 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(71, 85, 105, 0.6),
                        stop:1 rgba(100, 116, 139, 0.5));
                    border: 2px solid rgba(255, 255, 255, 0.25);
                    border-radius: 4px;
                    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.7);
                }
            """)

            # القيمة المحسنة مع ألوان مخصصة
            value_widget = QLabel(str(value))

            # تحديد لون القيمة حسب المحتوى
            value_color = self.get_value_color(label, str(value))

            value_widget.setStyleSheet(f"""
                QLabel {{
                    color: {value_color};
                    font-size: 15px;
                    font-weight: 600;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.5 rgba(248, 250, 252, 0.12),
                        stop:1 rgba(241, 245, 249, 0.08));
                    border: 1px solid rgba(255, 255, 255, 0.18);
                    border-radius: 8px;
                    padding: 10px 15px;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                }}
                QLabel:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.15),
                        stop:0.5 rgba(139, 92, 246, 0.12),
                        stop:1 rgba(34, 197, 94, 0.10));
                    border: 1px solid rgba(255, 255, 255, 0.25);
                }}
            """)
            value_widget.setWordWrap(True)

            item_layout.addWidget(label_widget)
            item_layout.addWidget(value_widget, 1)

            section_layout.addWidget(item_widget)

        layout.addWidget(section_frame)

    def get_value_color(self, label, value):
        """تحديد لون القيمة حسب المحتوى - ألوان أوضح وأكثر تباينًا"""
        # ألوان أساسية أوضح وأكثر تباينًا
        colors = {
            'positive': '#00FF7F',      # أخضر نيون للإيجابي
            'negative': '#FF6B6B',      # أحمر نيون للسلبي
            'neutral': '#E2E8F0',       # رمادي فاتح للمحايد
            'warning': '#FFD700',       # ذهبي للتحذيرات
            'info': '#00BFFF',          # أزرق سماوي للمعلومات
            'success': '#32CD32',       # أخضر ليموني للنجاح
            'error': '#FF4500',         # برتقالي أحمر للأخطاء
            'special': '#DA70D6',       # بنفسجي نيون للمميز
            'default': '#FFFFFF'        # أبيض نقي افتراضي
        }

        # تحديد اللون حسب التسمية والقيمة
        if "رصيد" in label.lower() or "مالي" in label.lower():
            if "🟢" in value or "دائن" in value:
                return colors['positive']
            elif "🔴" in value or "مدين" in value:
                return colors['negative']
            else:
                return colors['neutral']
        elif "حالة" in label.lower():
            if "نشط" in value or "✅" in value:
                return colors['success']
            elif "غير نشط" in value or "❌" in value:
                return colors['error']
            else:
                return colors['warning']
        elif "بريد" in label.lower():
            if "صحيح" in value or "✅" in value:
                return colors['success']
            elif "خطأ" in value or "❌" in value:
                return colors['error']
            else:
                return colors['info']
        elif "تصنيف" in label.lower() or "مستوى" in label.lower():
            if "مميز" in value or "عالي" in value or "⭐" in value:
                return colors['special']
            elif "متوسط" in value:
                return colors['warning']
            else:
                return colors['neutral']
        elif "غير محدد" in value or "لا توجد" in value:
            return colors['neutral']
        else:
            return colors['default']

    def get_balance_color(self):
        """تحديد لون الرصيد"""
        if not self.supplier.balance or self.supplier.balance == 0:
            return "🟡"  # أصفر للصفر
        elif self.supplier.balance > 0:
            return "🟢"  # أخضر للموجب
        else:
            return "🔴"  # أحمر للسالب

    # دوال مساعدة مبسطة لعرض المعلومات - مطابقة للنموذج المرجعي
    def get_account_status(self):
        """حالة الحساب المبسطة"""
        if not self.supplier.balance or self.supplier.balance == 0:
            return "متوازن ⚖️"
        elif self.supplier.balance > 0:
            return "دائن 💚"
        else:
            return "مدين 🔴"

    def get_supplier_type(self):
        """تصنيف المورد"""
        balance = abs(self.supplier.balance) if self.supplier.balance else 0
        if balance > 50000:
            return "مورد رئيسي 🌟"
        elif balance > 10000:
            return "مورد مميز ⭐"
        else:
            return "مورد عادي 👤"

    def get_activity_level(self):
        """مستوى النشاط المبسط"""
        balance = abs(self.supplier.balance) if self.supplier.balance else 0
        if balance > 20000:
            return "نشاط عالي 🔥"
        elif balance > 5000:
            return "نشاط متوسط 📊"
        else:
            return "نشاط منخفض 📉"

    def get_credit_rating(self):
        """تقييم الائتمان"""
        balance = self.supplier.balance if self.supplier.balance else 0
        if balance > 0:
            return "ممتاز 🏆"
        elif balance == 0:
            return "جيد ✅"
        else:
            return "يحتاج متابعة ⚠️"

    def get_data_completeness(self):
        """مستوى اكتمال البيانات"""
        fields = [self.supplier.name, self.supplier.phone, self.supplier.email, self.supplier.address]
        filled = sum(1 for field in fields if field and field.strip())
        percentage = (filled / len(fields)) * 100
        if percentage == 100:
            return f"مكتمل {percentage:.0f}% ✅"
        elif percentage >= 75:
            return f"جيد {percentage:.0f}% 🟢"
        elif percentage >= 50:
            return f"متوسط {percentage:.0f}% 🟡"
        else:
            return f"ناقص {percentage:.0f}% 🔴"

    def get_additional_phones(self):
        """الأرقام الإضافية"""
        return "لا توجد أرقام إضافية"

    def get_email_status(self):
        """حالة البريد الإلكتروني"""
        if self.supplier.email:
            if "@" in self.supplier.email and "." in self.supplier.email:
                return "صحيح ✅"
            else:
                return "تنسيق خطأ ❌"
        else:
            return "غير محدد ⚪"

    def get_contact_methods(self):
        """طرق التواصل المتاحة"""
        methods = []
        if self.supplier.phone:
            methods.append("هاتف 📱")
        if self.supplier.email:
            methods.append("بريد 📧")
        if self.supplier.address:
            methods.append("عنوان 📍")
        return " | ".join(methods) if methods else "غير متاح"

    def get_creation_date(self):
        """تاريخ الإضافة"""
        if hasattr(self.supplier, 'date_added') and self.supplier.date_added:
            return self.supplier.date_added.strftime('%Y-%m-%d %H:%M')
        elif hasattr(self.supplier, 'created_at') and self.supplier.created_at:
            return self.supplier.created_at.strftime('%Y-%m-%d %H:%M')
        else:
            return "غير محدد"

    def get_last_update(self):
        """آخر تحديث"""
        return "غير متاح حالياً"

    def get_membership_duration(self):
        """مدة التعامل"""
        creation_date = None
        if hasattr(self.supplier, 'date_added') and self.supplier.date_added:
            creation_date = self.supplier.date_added
        elif hasattr(self.supplier, 'created_at') and self.supplier.created_at:
            creation_date = self.supplier.created_at

        if creation_date:
            from datetime import datetime
            days = (datetime.now() - creation_date).days
            if days < 30:
                return f"{days} يوم"
            elif days < 365:
                months = days // 30
                return f"{months} شهر"
            else:
                years = days // 365
                return f"{years} سنة"
        return "غير محدد"

    def get_transactions_count(self):
        """عدد المعاملات"""
        return "غير متاح حالياً"

    def get_total_transactions(self):
        """إجمالي المعاملات"""
        return "غير متاح حالياً"

    def get_additional_info(self):
        """معلومات إضافية"""
        info_parts = []
        creation_date = None
        if hasattr(self.supplier, 'date_added') and self.supplier.date_added:
            creation_date = self.supplier.date_added
        elif hasattr(self.supplier, 'created_at') and self.supplier.created_at:
            creation_date = self.supplier.created_at

        if creation_date:
            from datetime import datetime
            days_since_creation = (datetime.now() - creation_date).days
            info_parts.append(f"مضى {days_since_creation} يوم على الإضافة")

        if self.supplier.balance:
            balance_abs = abs(self.supplier.balance)
            if balance_abs > 1000:
                info_parts.append(f"رصيد كبير: {balance_abs:.0f} جنيه")

        return " | ".join(info_parts) if info_parts else "لا توجد معلومات إضافية"

    def get_account_summary(self):
        """ملخص الحساب"""
        balance = self.supplier.balance if self.supplier.balance else 0
        status = "دائن" if balance > 0 else "مدين" if balance < 0 else "متوازن"
        return f"الحساب {status} بمبلغ {abs(balance):.0f} جنيه"

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم المحسنة - مطابق تماماً للنموذج المرجعي"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 10px;  /* تقليل padding لصالح البيانات */
                margin: 5px 0;  /* تقليل margin لصالح البيانات */
                min-height: 65px;  /* تحديد ارتفاع أقل */
                max-height: 70px;  /* تحديد ارتفاع أقصى أقل */
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)  # تقليل المسافة بين الأزرار لاستغلال المساحة

        # زر الإغلاق - في المقدمة مكان التعديل
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        close_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # زر الطباعة - عرض أكبر لاستغلال المساحة
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        print_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(print_btn, 'emerald')
        print_btn.clicked.connect(self.print_info)

        # زر تصدير PDF - عرض أكبر لاستغلال المساحة
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        export_pdf_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(export_pdf_btn, 'info')
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر إضافة ملاحظة - عرض أكبر لاستغلال المساحة
        note_btn = QPushButton("📝 إضافة ملاحظة")
        note_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        note_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(note_btn, 'orange')
        note_btn.clicked.connect(self.add_note)

        # ترتيب الأزرار مع الإغلاق في المقدمة واستغلال كامل للمساحة
        buttons_layout.addWidget(close_btn)  # زر الإغلاق في المقدمة
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(note_btn)

        layout.addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق للنموذج المرجعي"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                # تصميم متطور مطابق للنموذج المرجعي
                colors = {
                    'emerald': ('#10b981', '#34d399'),
                    'danger': ('#ef4444', '#f87171'),
                    'info': ('#3b82f6', '#60a5fa'),
                    'orange': ('#f97316', '#fb923c')
                }

                color_pair = colors.get(button_type, ('#6B7280', '#9CA3AF'))

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[1]});
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        min-height: 20px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[0]});
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def print_info(self):
        """طباعة معلومات المورد المحسنة"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from datetime import datetime

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                # إعداد الخطوط
                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y = 100

                # العنوان الرئيسي
                painter.setFont(title_font)
                painter.drawText(100, y, f"تقرير تفصيلي للمورد: {self.supplier.name}")
                y += 80

                # المعلومات الأساسية
                painter.setFont(header_font)
                painter.drawText(100, y, "المعلومات الأساسية:")
                y += 40

                painter.setFont(normal_font)
                painter.drawText(120, y, f"المعرف: #{str(self.supplier.id).zfill(6)}")
                y += 30
                painter.drawText(120, y, f"الاسم: {self.supplier.name}")
                y += 30
                painter.drawText(120, y, f"البريد الإلكتروني: {self.supplier.email or 'غير محدد'}")
                y += 30
                painter.drawText(120, y, f"الهاتف: {self.supplier.phone or 'غير محدد'}")
                y += 30
                painter.drawText(120, y, f"العنوان: {self.supplier.address or 'غير محدد'}")
                y += 50

                # المعلومات المالية
                painter.setFont(header_font)
                painter.drawText(100, y, "المعلومات المالية:")
                y += 40

                painter.setFont(normal_font)
                balance = self.supplier.balance or 0
                painter.drawText(120, y, f"الرصيد الحالي: {balance:.0f} جنيه")
                y += 30
                painter.drawText(120, y, f"حالة الحساب: {self.get_account_status()}")
                y += 30
                painter.drawText(120, y, f"تصنيف المورد: {self.get_supplier_type()}")
                y += 50

                # معلومات التاريخ
                painter.setFont(header_font)
                painter.drawText(100, y, "معلومات التاريخ:")
                y += 40

                painter.setFont(normal_font)
                creation_date = self.get_creation_date()
                if creation_date != "غير محدد":
                    painter.drawText(120, y, f"تاريخ الإضافة: {creation_date}")
                    y += 30
                painter.drawText(120, y, f"مدة التعامل: {self.get_membership_duration()}")
                y += 30
                painter.drawText(120, y, f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                y += 50

                # الملاحظات
                if self.supplier.notes:
                    painter.setFont(header_font)
                    painter.drawText(100, y, "الملاحظات:")
                    y += 40

                    painter.setFont(normal_font)
                    painter.drawText(120, y, self.supplier.notes[:100] + "..." if len(self.supplier.notes) > 100 else self.supplier.notes)

                painter.end()

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"فشل في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات المورد إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont, QColor, QPen
            from PyQt5.QtCore import QRect
            from datetime import datetime

            # اختيار مكان الحفظ
            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير معلومات المورد إلى PDF",
                f"supplier_{self.supplier.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if not filename:
                return

            # إعداد الطابعة للـ PDF
            printer = QPrinter()
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(filename)
            printer.setPageSize(QPrinter.A4)

            # إنشاء الرسام
            painter = QPainter()
            painter.begin(printer)

            # إعداد الخطوط
            title_font = QFont("Arial", 18, QFont.Bold)
            subtitle_font = QFont("Arial", 14, QFont.Bold)
            content_font = QFont("Arial", 12)
            info_font = QFont("Arial", 10)

            # الحصول على أبعاد الصفحة
            page_rect = printer.pageRect()
            margin = 50
            content_rect = QRect(margin, margin,
                               page_rect.width() - 2*margin,
                               page_rect.height() - 2*margin)

            y_pos = content_rect.top()

            # رسم العنوان الرئيسي
            painter.setFont(title_font)
            painter.setPen(QColor(0, 0, 0))
            title_text = f"🏢 معلومات المورد: {self.supplier.name}"
            painter.drawText(content_rect.left(), y_pos, title_text)
            y_pos += 70

            # رسم خط فاصل
            painter.setPen(QPen(QColor(100, 100, 100), 2))
            painter.drawLine(content_rect.left(), y_pos, content_rect.right(), y_pos)
            y_pos += 50

            # المعلومات الأساسية
            painter.setFont(subtitle_font)
            painter.setPen(QColor(0, 0, 0))
            painter.drawText(content_rect.left(), y_pos, "المعلومات الأساسية:")
            y_pos += 40

            painter.setFont(content_font)
            painter.setPen(QColor(50, 50, 50))

            basic_info = [
                f"معرف المورد: {self.supplier.id}",
                f"الاسم: {self.supplier.name}",
                f"البريد الإلكتروني: {self.supplier.email or 'غير محدد'}",
                f"الهاتف: {self.supplier.phone or 'غير محدد'}",
                f"العنوان: {self.supplier.address or 'غير محدد'}",
                f"الرصيد: {self.supplier.balance or 0} جنيه"
            ]

            for line in basic_info:
                painter.drawText(content_rect.left() + 20, y_pos, line)
                y_pos += 30

            y_pos += 20

            # الملاحظات
            if self.supplier.notes:
                painter.setFont(subtitle_font)
                painter.setPen(QColor(0, 0, 0))
                painter.drawText(content_rect.left(), y_pos, "الملاحظات:")
                y_pos += 40

                painter.setFont(content_font)
                painter.setPen(QColor(50, 50, 50))

                # تقسيم الملاحظات إلى أسطر
                notes_lines = self.supplier.notes.split('\n')
                for line in notes_lines:
                    if y_pos + 30 > content_rect.bottom():
                        printer.newPage()
                        y_pos = content_rect.top()

                    painter.drawText(content_rect.left() + 20, y_pos, line)
                    y_pos += 30

                y_pos += 20

            # معلومات التصدير
            y_pos += 30
            painter.setPen(QPen(QColor(100, 100, 100), 1))
            painter.drawLine(content_rect.left(), y_pos, content_rect.right(), y_pos)
            y_pos += 30

            painter.setFont(info_font)
            painter.setPen(QColor(100, 100, 100))
            export_info = f"تم التصدير في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            painter.drawText(content_rect.left(), y_pos, export_info)

            painter.end()

            from utils import show_info_message
            show_info_message("نجح", f"تم تصدير معلومات المورد إلى:\n{filename}")

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"فشل في التصدير: {str(e)}")

    def add_note(self):
        """فتح نافذة إضافة ملاحظة متطورة"""
        try:
            dialog = AddSupplierNoteDialog(self, self.supplier, self.parent_widget)
            if dialog.exec_() == QDialog.Accepted:
                # تحديث العرض في النافذة الحالية
                self.refresh_supplier_info()

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"فشل في فتح نافذة الملاحظات: {str(e)}")

    def refresh_supplier_info(self):
        """تحديث معلومات المورد"""
        try:
            # إعادة تحميل بيانات المورد من قاعدة البيانات
            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                updated_supplier = self.parent_widget.session.query(Supplier).get(self.supplier.id)
                if updated_supplier:
                    self.supplier = updated_supplier
                    # إعادة إعداد الواجهة
                    self.setup_ui()
        except Exception as e:
            print(f"خطأ في تحديث معلومات المورد: {str(e)}")





    def create_shadow_effect(self):
        """إنشاء تأثير الظل للنافذة"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 100))
        return shadow

    def create_window_icon(self):
        """إنشاء أيقونة النافذة"""
        pixmap = QPixmap(48, 48)
        pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        gradient = QRadialGradient(24, 24, 20)
        gradient.setColorAt(0, QColor(59, 130, 246))
        gradient.setColorAt(0.7, QColor(29, 78, 216))
        gradient.setColorAt(1, QColor(30, 64, 175))

        painter.setBrush(QBrush(gradient))
        painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
        painter.drawEllipse(4, 4, 40, 40)
        painter.setPen(QPen(QColor(255, 255, 255), 3))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.drawText(12, 30, "🏢")
        painter.end()

        return QIcon(pixmap)

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(59, 130, 246))
            gradient.setColorAt(0.7, QColor(29, 78, 216))
            gradient.setColorAt(1, QColor(30, 64, 175))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "🏢")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════════
    # الدوال المرجعية للتصميم - مطابقة للنموذج المرجعي
    # ═══════════════════════════════════════════════════════════════════════════════════

    @staticmethod
    def get_reference_colors():
        """الحصول على الألوان المرجعية للنظام"""
        return {
            'positive': '#00FF7F',      # أخضر نيون للإيجابي
            'negative': '#FF6B6B',      # أحمر نيون للسلبي
            'neutral': '#E2E8F0',       # رمادي فاتح للمحايد
            'warning': '#FFD700',       # ذهبي للتحذيرات
            'info': '#00BFFF',          # أزرق سماوي للمعلومات
            'vip': '#FFD700',           # ذهبي للمميزين
            'new': '#00FFFF',           # سماوي نيون للجدد
            'normal': '#DA70D6',        # بنفسجي نيون للعاديين
            'high': '#FF4500',          # برتقالي أحمر للعالي
            'medium': '#FFD700',        # ذهبي للمتوسط
            'low': '#C0C0C0',           # فضي للمنخفض
            'default': '#FFFFFF'        # أبيض نقي افتراضي
        }

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي للنوافذ"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: 4px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """


class AddSupplierNoteDialog(QDialog):
    """نافذة ملاحظات المورد - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, supplier=None, parent_widget=None):
        super().__init__(parent)
        self.supplier = supplier
        self.parent_widget = parent_widget
        self.setup_ui()

    def setup_ui(self):
        """إعداد نافذة بسيطة جداً - مطابق للنموذج المرجعي"""
        self.setWindowTitle(f"📝 {self.supplier.name if self.supplier.name else 'مورد'}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان الخارجي ليكون أسود
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(SupplierInfoDialog.get_reference_styling())

        # تخطيط بسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # شريط العنوان الداخلي مطابق للنموذج المرجعي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(8, 5, 8, 11)  # رفع النص 3 درجات (8-3=5 للأعلى، 8+3=11 للأسفل)
        title_layout.setSpacing(10)

        # نص العنوان في المنتصف - مكبر ومرفوع
        title_text = QLabel(f"📝 ملاحظة للمورد: {self.supplier.name}")
        title_text.setAlignment(Qt.AlignCenter)
        title_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: transparent;
                border: none;
                padding: 4px 15px;
                margin: 0px;
            }
        """)

        # وضع النص في المنتصف تماماً
        title_layout.addStretch()
        title_layout.addWidget(title_text)
        title_layout.addStretch()
        layout.addWidget(title_frame)

        # محرر النصوص المتطور مطابق للنموذج المرجعي
        self.text_editor = QTextEdit()
        self.text_editor.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(248, 250, 252, 0.1));
                color: #FFFFFF;
                font-size: 14px;
                font-family: 'Segoe UI', Arial, sans-serif;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 10px;
                selection-background-color: rgba(59, 130, 246, 0.5);
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid rgba(255, 215, 0, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:1 rgba(248, 250, 252, 0.15));
            }
        """)
        self.text_editor.setPlaceholderText("اكتب ملاحظاتك هنا...\n\nيمكنك كتابة معلومات مفصلة عن المورد، تذكيرات، أو أي ملاحظات مهمة.")
        self.text_editor.setMinimumHeight(180)
        layout.addWidget(self.text_editor)

        # أزرار بسيطة
        self.create_buttons(layout)

        # تحميل النص
        self.load_note()

    def create_buttons(self, layout):
        """أزرار متطورة مطابقة للنموذج المرجعي"""
        # إطار الأزرار مطابق للنموذج المرجعي
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                margin: 5px 0px;
                max-height: 80px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        buttons_layout.setSpacing(20)

        # زر الإلغاء أولاً
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumWidth(140)
        cancel_btn.setMinimumHeight(45)
        self.apply_reference_button_style(cancel_btn, 'danger')
        cancel_btn.clicked.connect(self.reject)

        # زر الحفظ ثانياً
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumWidth(140)
        save_btn.setMinimumHeight(45)
        self.apply_reference_button_style(save_btn, 'success')
        save_btn.clicked.connect(self.save_note)

        # وضع الأزرار في المنتصف
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

    def apply_reference_button_style(self, button, button_type):
        """تطبيق تصميم الأزرار المرجعي المتطور"""
        # استخدام نفس التصميم المتطور من النموذج المرجعي
        if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(button, button_type)
        else:
            # تصميم متطور مطابق للنموذج المرجعي
            colors = {
                'success': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'border': '#10b981', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'border': '#dc2626', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'border': '#0ea5e9', 'shadow': 'rgba(14, 165, 233, 0.6)'
                }
            }

            color_set = colors.get(button_type, colors['success'])

            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['bg_start']},
                        stop:0.15 {color_set['bg_mid']},
                        stop:0.85 {color_set['bg_end']},
                        stop:1 {color_set['bg_bottom']});
                    color: #ffffff;
                    border: 4px solid {color_set['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_set['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.2);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['hover_start']},
                        stop:0.15 {color_set['hover_mid']},
                        stop:0.85 {color_set['hover_end']},
                        stop:1 {color_set['hover_bottom']});
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_set['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3);
                }}
                QPushButton:pressed {{
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_set['shadow']},
                               inset 0 2px 0 rgba(0, 0, 0, 0.2),
                               inset 0 -1px 0 rgba(255, 255, 255, 0.2);
                }}
            """)

    def load_note(self):
        """تحميل الملاحظة الحالية"""
        if self.supplier and self.supplier.notes:
            self.text_editor.setPlainText(self.supplier.notes)

    def save_note(self):
        """حفظ الملاحظة - مطابق للنموذج المرجعي"""
        try:
            note = self.text_editor.toPlainText().strip()
            self.supplier.notes = note if note else None

            if self.parent_widget and self.parent_widget.session:
                self.parent_widget.session.commit()
                if hasattr(self.parent_widget, 'refresh_data'):
                    self.parent_widget.refresh_data()

            self.accept()

            from utils import show_info_message
            show_info_message("تم", "حُفظت الملاحظة")

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"فشل الحفظ: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي ليكون أسود"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            # تطبيق تصميم أسود بسيط للشريط
            pass


class EditSupplierAmountDialog(QDialog):
    """نافذة بسيطة لتعديل مبلغ المورد - إضافة أو تقليل فقط"""

    def __init__(self, parent=None, supplier=None, session=None):
        super().__init__(parent)
        self.supplier = supplier
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة البسيطة"""
        # إعداد النافذة الأساسي
        self.setWindowTitle(f"💰 تعديل رصيد المورد: {self.supplier.name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 300)

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel(f"💰 تعديل رصيد المورد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                padding: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6), stop:1 rgba(139, 92, 246, 0.6));
                border-radius: 12px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # معلومات المورد
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 10px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)

        # اسم المورد
        name_label = QLabel(f"🏢 المورد: {self.supplier.name}")
        name_label.setStyleSheet("color: #ffffff; font-size: 14px; font-weight: bold;")
        info_layout.addWidget(name_label)

        # الرصيد الحالي
        current_balance = self.supplier.balance or 0
        balance_label = QLabel(f"💳 الرصيد الحالي: {format_currency(current_balance)}")
        if current_balance > 0:
            balance_color = "#f87171"  # أحمر للدين
        elif current_balance < 0:
            balance_color = "#34d399"  # أخضر للرصيد الموجب
        else:
            balance_color = "#d1d5db"  # رمادي للصفر
        balance_label.setStyleSheet(f"color: {balance_color}; font-size: 14px; font-weight: bold;")
        info_layout.addWidget(balance_label)

        layout.addWidget(info_frame)

        # حقل تعديل المبلغ
        amount_frame = QFrame()
        amount_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        amount_layout = QFormLayout(amount_frame)

        # تسمية المبلغ
        amount_label = QLabel("المبلغ المراد إضافته أو تقليله:")
        amount_label.setStyleSheet("color: #ffffff; font-weight: bold; font-size: 13px;")

        # حقل المبلغ
        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setRange(-999999, 999999)
        self.amount_edit.setDecimals(0)
        self.amount_edit.setSingleStep(100)
        self.amount_edit.setValue(0)
        self.amount_edit.setPrefix("💰 ")
        self.amount_edit.setSuffix(" جنيه")
        self.amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                color: #1e293b;
            }
            QDoubleSpinBox:focus {
                border: 3px solid rgba(59, 130, 246, 0.8);
                background: rgba(219, 234, 254, 0.9);
            }
        """)

        amount_layout.addRow(amount_label, self.amount_edit)

        # ملاحظة توضيحية
        note_label = QLabel("💡 القيم الموجبة تزيد الرصيد، والقيم السالبة تقلل الرصيد")
        note_label.setStyleSheet("color: #fbbf24; font-size: 12px; font-style: italic;")
        amount_layout.addRow(note_label)

        layout.addWidget(amount_frame)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر الحفظ
        save_button = QPushButton("💾 حفظ التعديل")
        save_button.clicked.connect(self.save_changes)
        save_button.setMinimumHeight(40)
        save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10b981, stop:1 #059669);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 13px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #34d399, stop:1 #10b981);
                border: 3px solid rgba(255, 255, 255, 0.5);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #059669, stop:1 #047857);
            }
        """)

        # زر الإلغاء
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setMinimumHeight(40)
        cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ef4444, stop:1 #dc2626);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 13px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f87171, stop:1 #ef4444);
                border: 3px solid rgba(255, 255, 255, 0.5);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #dc2626, stop:1 #b91c1c);
            }
        """)

        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

    def save_changes(self):
        """حفظ التعديل على رصيد المورد"""
        try:
            amount = self.amount_edit.value()

            # التحقق من صحة المبلغ
            if amount == 0:
                show_error_message("تحذير", "يجب إدخال مبلغ غير صفر")
                return

            # حفظ الرصيد القديم للمقارنة
            old_balance = self.supplier.balance or 0

            # تحديث رصيد المورد
            self.supplier.balance = old_balance + amount
            new_balance = self.supplier.balance

            # حفظ التغييرات في قاعدة البيانات
            self.session.commit()

            # إظهار رسالة نجاح
            if amount > 0:
                operation = "إضافة"
                icon = "➕"
            else:
                operation = "تقليل"
                icon = "➖"

            show_info_message(
                "تم بنجاح",
                f"{icon} تم {operation} {format_currency(abs(amount))} {operation} رصيد المورد {self.supplier.name}\n\n"
                f"📊 الرصيد السابق: {format_currency(old_balance)}\n"
                f"📊 الرصيد الجديد: {format_currency(new_balance)}"
            )

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            # التراجع عن التغييرات في حالة الخطأ
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء حفظ التعديل:\n{str(e)}")



