import sys
import datetime
import platform
import ctypes
from ctypes import wintypes
import csv
import json
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QFrame, QComboBox, QSizePolicy, QMenu, QAction,
                            QDialog, QFormLayout, QTextEdit, QDoubleSpinBox, QMessageBox,
                            QDateEdit, QTabWidget, QSplitter, QListWidget, QListWidgetItem,
                            QTextBrowser, QFileDialog, QAbstractItemView, QScrollArea)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import (QIcon, QFont, QColor, QPainter, QPixmap, QBrush, QPen,
                        QLinearGradient, QRadialGradient, QTextDocument)
from PyQt5.QtPrintSupport import QPrinter

from database import Sale, Client, get_session
from utils import show_error_message, show_info_message, format_currency, safe_edit_item
from ui.unified_styles import UnifiedStyles
from ui.common_dialogs import WarningDialog
from ui.title_bar_utils import TitleBarStyler
from sqlalchemy import func


class DeleteSaleDialog(QDialog):
    """نافذة حذف المبيعة مشابهة لنافذة حذف العميل"""

    def __init__(self, parent=None, sale_data=None):
        super().__init__(parent)
        self.sale_data = sale_data
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف العميل"""
        self.setWindowTitle("💰 حذف - نظام إدارة المبيعات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("💰 حذف المبيعة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات المبيعة مضغوطة
        if self.sale_data:
            info_text = f"💰 {self.sale_data.get('item', 'عنصر')[:15]}{'...' if len(self.sale_data.get('item', '')) > 15 else ''}"
            if self.sale_data.get('quantity'):
                info_text += f" | 📊 {self.sale_data.get('quantity')}"
            if self.sale_data.get('price'):
                info_text += f" | 💰 {self.sale_data.get('price')}"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("💰 حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "💰")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)


class SalesWidget(QWidget):
    """واجهة إدارة المبيعات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()

        # تحميل البيانات تلقائياً عند بدء التشغيل
        QTimer.singleShot(100, self.refresh_data)


    def init_ui(self):
        # إنشاء التخطيط الرئيسي مع تقليل المساحات الفارغة لاستغلال المساحة للجدول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(3)  # تقليل المسافات من 8 إلى 3

        # العنوان الرئيسي مطابق للفواتير
        title_label = QLabel("💰 إدارة المبيعات المتطورة - نظام شامل ومتقدم لإدارة المبيعات مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إطار البحث
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # البحث مطابق للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعميل، رقم الفاتورة أو المنتج...")
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        # فلتر الحالة مطابق للفواتير
        status_label = QLabel("📊 حالة:")
        status_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        status_label.setAlignment(Qt.AlignCenter)

        # إنشاء قائمة تصفية مخصصة ومطورة للحالات مطابقة للفواتير
        self.create_custom_status_filter()


        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit, 1)
        search_layout.addWidget(status_label)
        search_layout.addWidget(self.status_filter_frame)
        search_layout.addStretch()

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        search_frame.setLayout(top_container)
        main_layout.addWidget(search_frame)

        # جدول المبيعات
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(11)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للحقول الجديدة
        headers = [
            "🔢 ID",
            "🔢 رقم المبيعة",
            "🧑‍💼 العميل",
            "📅 التاريخ",
            "💰 الإجمالي",
            "💵 المدفوع",
            "🏷️ الخصم",
            "📊 الضريبة",
            "🎯 الحالة",
            "💳 طريقة الدفع",
            "📋 ملاحظات"
        ]
        self.sales_table.setHorizontalHeaderLabels(headers)

        # إعداد التحديد للسطر كاملاً
        self.sales_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sales_table.setSelectionMode(QAbstractItemView.SingleSelection)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.sales_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.sales_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.sales_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: bold;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                /* color: #1e293b; */ /* تم إزالة اللون الثابت للسماح بألوان مخصصة */
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.sales_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.sales_table.verticalHeader().setDefaultSectionSize(45)
        self.sales_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_sales_table()

        # إعدادات عرض الأعمدة للجدول الجديد (11 عمود)
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # رقم المبيعة
        header.setSectionResizeMode(2, QHeaderView.Fixed)   # العميل
        header.setSectionResizeMode(3, QHeaderView.Stretch) # التاريخ
        header.setSectionResizeMode(4, QHeaderView.Stretch) # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Stretch) # المدفوع
        header.setSectionResizeMode(6, QHeaderView.Stretch) # الخصم
        header.setSectionResizeMode(7, QHeaderView.Stretch) # الضريبة
        header.setSectionResizeMode(8, QHeaderView.Stretch) # الحالة
        header.setSectionResizeMode(9, QHeaderView.Stretch) # طريقة الدفع
        header.setSectionResizeMode(10, QHeaderView.Stretch) # ملاحظات

        # تحديد عرض الأعمدة الثابتة
        self.sales_table.setColumnWidth(0, 80)   # ID
        self.sales_table.setColumnWidth(1, 120)  # رقم المبيعة
        self.sales_table.setColumnWidth(2, 200)  # العميل

        # إضافة معالج التمرير المخصص
        def sales_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.sales_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.sales_table, event)

        self.sales_table.wheelEvent = sales_wheelEvent

        main_layout.addWidget(self.sales_table, 1)

        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 3px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(8, 3, 8, 3)  # تقليل المسافات بنسبة بسيطة
        buttons_layout.setSpacing(8)  # تقليل المسافة بين الأزرار قليلاً

        # أزرار العمليات مثل المخزون مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة مبيعة")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_sale)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_sale)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_sale)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'indigo')  # بنفسجي للتفاصيل - بدون قائمة
        self.view_button.clicked.connect(self.view_sale_details)  # ربط مباشر بدون قائمة
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير المتقدمة مطابقة لجميع الأقسام
        export_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 12px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 13px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                           0 5px 15px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 10px 15px;
                margin: 2px;
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 700;
                font-size: 13px;
                text-align: center;
                min-height: 20px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 6px 12px;
                border: none;
                border-radius: 1px;
            }
        """)

        # قسم التصدير الأساسي
        excel_action = QAction("📊 تصدير Excel متقدم", self)
        excel_action.triggered.connect(self.export_excel_advanced)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV شامل", self)
        csv_action.triggered.connect(self.export_csv_advanced)
        export_menu.addAction(csv_action)

        pdf_action = QAction("📋 تصدير PDF تفصيلي", self)
        pdf_action.triggered.connect(self.export_pdf_advanced)
        export_menu.addAction(pdf_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التقارير المتقدمة
        customer_report_action = QAction("👥 تقرير العملاء", self)
        customer_report_action.triggered.connect(self.export_customer_report)
        export_menu.addAction(customer_report_action)

        revenue_analysis_action = QAction("💰 تحليل الإيرادات", self)
        revenue_analysis_action.triggered.connect(self.export_revenue_analysis)
        export_menu.addAction(revenue_analysis_action)

        monthly_report_action = QAction("📅 التقرير الشهري", self)
        monthly_report_action.triggered.connect(self.export_monthly_report)
        export_menu.addAction(monthly_report_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التصدير المخصص
        custom_action = QAction("⚙️ تصدير مخصص", self)
        custom_action.triggered.connect(self.export_custom)
        export_menu.addAction(custom_action)

        backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        backup_action.triggered.connect(self.export_backup)
        export_menu.addAction(backup_action)

        restore_action = QAction("📥 استعادة نسخة احتياطية", self)
        restore_action.triggered.connect(self.restore_backup)
        export_menu.addAction(restore_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي المبيعات مطور ليتشابه مع الفواتير
        self.total_sales_label = QLabel("إجمالي المبيعات: 0 | القيمة الإجمالية: 0.00 ج.م")
        self.total_sales_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_sales_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addWidget(self.refresh_button)
        buttons_layout.addWidget(self.view_button)
        buttons_layout.addWidget(self.export_button)
        buttons_layout.addWidget(self.statistics_button)
        buttons_layout.addWidget(self.total_sales_label)

        buttons_frame.setLayout(buttons_layout)
        main_layout.addWidget(buttons_frame)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث البحث
            self.search_edit.textChanged.connect(self.filter_sales)

            # ربط خاصية النقر المزدوج للتعديل مطابق للعملاء
            self.sales_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

            print("✅ تم ربط أحداث المبيعات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث المبيعات: {str(e)}")

    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية مطابق للعملاء"""
        try:
            self.edit_sale()
        except Exception as e:
            pass  # خطأ في النقر المزدوج

    def filter_sales(self):
        """تصفية المبيعات"""
        search_text = self.search_edit.text().lower()
        status_filter = getattr(self, 'current_status_value', 'all')
        
        for row in range(self.sales_table.rowCount()):
            show_row = True
            
            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.sales_table.columnCount()):
                    item = self.sales_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                
                if search_text not in row_text:
                    show_row = False
            
            # فلترة الحالة
            if status_filter != "all":
                status_item = self.sales_table.item(row, 7)
                if status_item and status_filter not in status_item.text():
                    show_row = False
            
            self.sales_table.setRowHidden(row, not show_row)
        
        # تحديث الملخص بعد التصفية
        self.update_summary()



    def add_sale(self):
        """إضافة مبيعة جديدة"""
        try:
            dialog = SaleDialog(self, None, self.session)
            if dialog.exec_() == QDialog.Accepted:
                show_info_message("تم", "تم إضافة المبيعة بنجاح")
                self.refresh_data()  # تحديث الجدول بعد الإضافة
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إضافة المبيعة: {str(e)}")

    def edit_sale(self):
        """تعديل مبيعة"""
        safe_edit_item(self, self.sales_table, Sale, SaleDialog, self.session, "مبيعة")

    def save_sale_changes(self, row, invoice, customer, date, product, quantity, unit_price, total, status, notes, dialog):
        """حفظ تعديلات المبيعة"""
        try:
            # تحديث البيانات في الجدول
            self.sales_table.setItem(row, 0, QTableWidgetItem(invoice))
            self.sales_table.setItem(row, 1, QTableWidgetItem(customer))
            self.sales_table.setItem(row, 2, QTableWidgetItem(date))
            self.sales_table.setItem(row, 3, QTableWidgetItem(product))
            self.sales_table.setItem(row, 4, QTableWidgetItem(quantity))
            self.sales_table.setItem(row, 5, QTableWidgetItem(unit_price))
            self.sales_table.setItem(row, 6, QTableWidgetItem(total))
            self.sales_table.setItem(row, 7, QTableWidgetItem(status))
            self.sales_table.setItem(row, 8, QTableWidgetItem(notes))

            # تطبيق التنسيق
            for col in range(9):
                item = self.sales_table.item(row, col)
                if item:
                    item.setFont(QFont("Arial", 10, QFont.Bold))
                    item.setForeground(QColor("#000000"))

                    # تلوين حسب الحالة
                    if col == 7:  # عمود الحالة
                        if status == "مكتمل":
                            item.setBackground(QColor("#d1fae5"))
                        elif status == "معلق":
                            item.setBackground(QColor("#fef3c7"))
                        elif status == "ملغي":
                            item.setBackground(QColor("#fee2e2"))

            QMessageBox.information(self, "تم", "تم حفظ التعديلات بنجاح!")
            dialog.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ التعديلات: {str(e)}")

    def delete_sale(self):
        """حذف مبيعة مع نافذة تأكيد متطورة"""
        try:
            current_row = self.sales_table.currentRow()
            if current_row < 0:
                self.show_warning_message("يرجى اختيار مبيعة للحذف")
                return

            # الحصول على بيانات المبيعة من الجدول
            sale_data = self.get_sale_data_from_row(current_row)
            if not sale_data:
                self.show_error_message("لا يمكن تحديد المبيعة المحددة")
                return

            # إنشاء نافذة حذف متطورة مشابهة للعملاء
            dialog = DeleteSaleDialog(self, sale_data)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # حذف المبيعة من الجدول
                    self.sales_table.removeRow(current_row)
                    self.update_summary()

                    # إظهار رسالة نجاح متطورة
                    self.show_success_message(f"تم حذف المبيعة بنجاح")

                except Exception as e:
                    self.show_error_message(f"فشل في حذف المبيعة: {str(e)}")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف المبيعة: {str(e)}")

    def get_sale_data_from_row(self, row):
        """استخراج بيانات المبيعة من الصف المحدد"""
        try:
            if row < 0 or row >= self.sales_table.rowCount():
                return None

            # استخراج البيانات من الجدول
            client = self.sales_table.item(row, 0).text() if self.sales_table.item(row, 0) else ""
            item = self.sales_table.item(row, 1).text() if self.sales_table.item(row, 1) else ""
            quantity = self.sales_table.item(row, 2).text() if self.sales_table.item(row, 2) else ""
            price = self.sales_table.item(row, 3).text() if self.sales_table.item(row, 3) else ""

            return {
                'client': client,
                'item': item,
                'quantity': quantity,
                'price': price
            }
        except Exception as e:
            print(f"خطأ في استخراج بيانات المبيعة: {e}")
            return None

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مشابهة لنوافذ البرنامج"""
        dialog = WarningDialog(self, message)
        dialog.exec_()

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("✅ نجح")
        msg.setText(message)
        msg.exec_()

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("❌ خطأ")
        msg.setText(message)
        msg.exec_()

    def add_watermark_to_sales_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.sales_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.sales_table.viewport())
                paint_watermark(painter, self.sales_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.sales_table.paintEvent = new_paint_event
        # إجبار إعادة الرسم
        self.sales_table.viewport().update()
        self.sales_table.repaint()

    def refresh_data(self):
        """تحديث البيانات من قاعدة البيانات مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True

            # تعطيل زر التحديث مؤقتاً
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("🔄 جاري التحديث...")

            # حفظ السطر المحدد حالياً
            current_row = self.sales_table.currentRow()

            # مسح الجدول
            self.sales_table.setRowCount(0)

            # تحميل البيانات من قاعدة البيانات
            sales = self.session.query(Sale).all()

            # ملء الجدول بالبيانات
            for sale in sales:
                self.add_sale_to_table(sale)

            self.update_summary()

            # استعادة التحديد إذا كان ممكناً
            if current_row >= 0 and current_row < self.sales_table.rowCount():
                self.sales_table.selectRow(current_row)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تحديث البيانات: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

    def add_sale_to_table(self, sale):
        """إضافة مبيعة إلى الجدول"""
        try:
            row = self.sales_table.rowCount()
            self.sales_table.insertRow(row)

            # الحصول على اسم العميل
            client_name = sale.client.name if sale.client else "عميل نقدي"

            # تحويل الحالة وطريقة الدفع إلى العربية للعرض
            status_map = {
                'pending': 'معلق',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'returned': 'مرتجع'
            }
            payment_method_map = {
                'cash': 'نقدي',
                'credit': 'ائتمان',
                'bank_transfer': 'تحويل بنكي',
                'check': 'شيك'
            }

            status_ar = status_map.get(sale.status, sale.status or 'معلق')
            payment_method_ar = payment_method_map.get(sale.payment_method, sale.payment_method or 'نقدي')

            # إضافة البيانات إلى الجدول بالترتيب الصحيح حسب العناوين:
            # "🔢 ID", "🔢 رقم المبيعة", "🧑‍💼 العميل", "📅 التاريخ", "💰 الإجمالي",
            # "💵 المدفوع", "🏷️ الخصم", "📊 الضريبة", "🎯 الحالة", "💳 طريقة الدفع", "📋 ملاحظات"
            items = [
                str(sale.id),  # ID
                getattr(sale, 'sale_number', None) or "غير محدد",  # رقم المبيعة
                client_name,  # العميل
                sale.date.strftime("%Y-%m-%d") if sale.date else "غير محدد",  # التاريخ
                f"{getattr(sale, 'total_amount', 0):.0f} جنيه",  # الإجمالي
                f"{getattr(sale, 'paid_amount', 0):.0f} جنيه",  # المدفوع
                f"{getattr(sale, 'discount_amount', 0):.0f} جنيه",  # الخصم
                f"{getattr(sale, 'tax_amount', 0):.0f} جنيه",  # الضريبة
                status_ar,  # الحالة
                payment_method_ar,  # طريقة الدفع
                getattr(sale, 'notes', None) or "لا توجد ملاحظات"  # الملاحظات
            ]

            # دالة مساعدة لإنشاء العناصر مطابقة للعملاء
            def create_item(text, default="No Data"):
                display_text = text if text and str(text).strip() else default
                item = QTableWidgetItem(str(display_text))
                item.setTextAlignment(Qt.AlignCenter)
                if display_text == default:
                    item.setForeground(QColor("#ef4444"))
                return item

            for col, item_text in enumerate(items):
                if col == 0:  # ID - لون أسود مطابق للعملاء
                    item = QTableWidgetItem(f"🔢 {item_text}")
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setForeground(QColor("#000000"))
                elif col == 8:  # عمود الحالة
                    item = QTableWidgetItem(f"🎯 {item_text}")
                    item.setTextAlignment(Qt.AlignCenter)
                    # تلوين الحالة
                    if sale.status == "completed":
                        item.setBackground(QColor("#d1fae5"))
                        item.setForeground(QColor("#065f46"))
                    elif sale.status == "pending":
                        item.setBackground(QColor("#fef3c7"))
                        item.setForeground(QColor("#92400e"))
                    elif sale.status == "cancelled":
                        item.setBackground(QColor("#fee2e2"))
                        item.setForeground(QColor("#991b1b"))
                    elif sale.status == "returned":
                        item.setBackground(QColor("#fde2e7"))
                        item.setForeground(QColor("#be185d"))
                else:
                    # باقي الأعمدة مع الأيقونات
                    icons = ["", "🔢", "🧑‍💼", "📅", "💰", "💵", "🏷️", "📊", "", "💳", "📋"]
                    if col < len(icons) and icons[col]:
                        item = create_item(f"{icons[col]} {item_text}")
                    else:
                        item = create_item(item_text)

                    # تلوين المبالغ المالية
                    if col in [4, 5, 6, 7]:
                        if col == 4:  # الإجمالي
                            item.setForeground(QColor("#1f2937"))
                        elif col == 5:  # المدفوع
                            item.setForeground(QColor("#059669"))
                        elif col == 6:  # الخصم
                            item.setForeground(QColor("#dc2626"))
                        elif col == 7:  # الضريبة
                            item.setForeground(QColor("#7c2d12"))

                self.sales_table.setItem(row, col, item)

        except Exception as e:
            print(f"خطأ في إضافة المبيعة إلى الجدول: {e}")



    def update_summary(self):
        """تحديث ملخص المبيعات"""
        try:
            total_count = 0
            total_value = 0.0
            
            for row in range(self.sales_table.rowCount()):
                if not self.sales_table.isRowHidden(row):
                    total_count += 1
                    total_item = self.sales_table.item(row, 6)  # عمود الإجمالي
                    if total_item:
                        # إزالة الأيقونات والنصوص الإضافية واستخراج الرقم فقط
                        text = total_item.text().replace('💵', '').replace('ج.م', '').replace(',', '').strip()
                        if text and text.replace('.', '').replace('-', '').isdigit():
                            total_value += float(text)
            
            self.total_sales_label.setText(f"إجمالي المبيعات: {total_count} | القيمة الإجمالية: {total_value:.2f} ج.م")
            
        except Exception as e:
            print(f"خطأ في تحديث الملخص: {str(e)}")

    def show_statistics(self):
        """عرض نافذة إحصائيات المبيعات"""
        try:
            dialog = SalesStatisticsDialog(self.session, self)
            dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض إحصائيات المبيعات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")




    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر المتطور: {str(e)}")

    def export_to_excel(self):
        """تصدير المبيعات إلى Excel (CSV)"""
        self.export_to_csv()

    def export_to_csv(self):
        """تصدير المبيعات إلى CSV"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ ملف CSV", "المبيعات.csv", "CSV Files (*.csv)")
            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    headers = ['الرقم', 'رقم المبيعة', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'الحالة']
                    writer.writerow(headers)

                    for row in range(self.sales_table.rowCount()):
                        row_data = []
                        for col in range(self.sales_table.columnCount()):
                            item = self.sales_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)
                QMessageBox.information(self, "تم", f"تم تصدير المبيعات بنجاح إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير المبيعات إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المبيعات", "تقرير_المبيعات.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المبيعات</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #6366f1; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>💰 تقرير المبيعات</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>رقم المبيعة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                        </tr>
                """

                total_amount = 0
                for row in range(self.sales_table.rowCount()):
                    sale_id = self.sales_table.item(row, 0).text() if self.sales_table.item(row, 0) else ""
                    sale_number = self.sales_table.item(row, 1).text() if self.sales_table.item(row, 1) else ""
                    customer = self.sales_table.item(row, 2).text() if self.sales_table.item(row, 2) else ""
                    date = self.sales_table.item(row, 3).text() if self.sales_table.item(row, 3) else ""
                    amount_text = self.sales_table.item(row, 4).text() if self.sales_table.item(row, 4) else "0"
                    status = self.sales_table.item(row, 5).text() if self.sales_table.item(row, 5) else ""

                    try:
                        amount = float(amount_text.replace(',', '').replace('جنيه', '').strip())
                        total_amount += amount
                    except:
                        amount = 0

                    html_content += f"""
                        <tr>
                            <td>{sale_id}</td>
                            <td>{sale_number}</td>
                            <td>{customer}</td>
                            <td>{date}</td>
                            <td>{int(amount):,} جنيه</td>
                            <td>{status}</td>
                        </tr>
                    """

                html_content += f"""
                    </table>
                    <h3>إجمالي المبيعات: {int(total_amount):,} جنيه</h3>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                QMessageBox.information(self, "تم", f"تم تصدير المبيعات إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير المبيعات إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "المبيعات.json", "JSON Files (*.json)"
            )

            if file_path:
                data = []
                for row in range(self.sales_table.rowCount()):
                    row_data = {
                        'الرقم': self.sales_table.item(row, 0).text() if self.sales_table.item(row, 0) else "",
                        'رقم المبيعة': self.sales_table.item(row, 1).text() if self.sales_table.item(row, 1) else "",
                        'العميل': self.sales_table.item(row, 2).text() if self.sales_table.item(row, 2) else "",
                        'التاريخ': self.sales_table.item(row, 3).text() if self.sales_table.item(row, 3) else "",
                        'المبلغ الإجمالي': self.sales_table.item(row, 4).text() if self.sales_table.item(row, 4) else "",
                        'الحالة': self.sales_table.item(row, 5).text() if self.sales_table.item(row, 5) else ""
                    }
                    data.append(row_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "تم", f"تم تصدير المبيعات إلى JSON بنجاح:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير JSON: {str(e)}")

    def export_statistics_report(self, stats_content):
        """تصدير تقرير الإحصائيات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإحصائيات", "إحصائيات_المبيعات.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            📊 تقرير إحصائيات المبيعات
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

{stats_content}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المبيعات
═══════════════════════════════════════════════════════════════════════════════
""")

                QMessageBox.information(self, "تم", f"تم تصدير تقرير الإحصائيات بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def view_sale_details(self):
        """عرض تفاصيل المبيعة المحددة"""
        try:
            selected_row = self.sales_table.currentRow()
            if selected_row < 0:
                show_error_message("خطأ", "الرجاء اختيار مبيعة من القائمة")
                return

            # استخراج معرف المبيعة مع التعامل مع الرموز التعبيرية
            id_text = self.sales_table.item(selected_row, 0).text()
            import re
            sale_id = int(re.sub(r'[^\d]', '', id_text))

            # البحث عن المبيعة في قاعدة البيانات
            sale = self.session.query(Sale).get(sale_id)
            if not sale:
                show_error_message("خطأ", "لم يتم العثور على المبيعة")
                return

            # إنشاء نافذة المعلومات المتطورة
            info_dialog = SaleInfoDialog(self, sale)
            info_dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"فشل في عرض تفاصيل المبيعة: {str(e)}")



    def print_sale_details(self, invoice, customer, date, product, quantity, unit_price, total, status, notes):
        """طباعة تفاصيل المبيعة"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تفاصيل المبيعة", f"تفاصيل_المبيعة_{invoice}.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            💰 تفاصيل المبيعة
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ الطباعة: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الطباعة: {QDate.currentDate().toString('hh:mm:ss')}

📋 معلومات المبيعة:
─────────────────────────────────────────────────────────────────────────────
رقم الفاتورة: {invoice}
العميل: {customer}
التاريخ: {date}
المنتج: {product}
الكمية: {quantity}
سعر الوحدة: {unit_price}
الإجمالي: {total}
الحالة: {status}
ملاحظات: {notes}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المبيعات
═══════════════════════════════════════════════════════════════════════════════
""")

                QMessageBox.information(self, "تم", f"تم حفظ تفاصيل المبيعة بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ التفاصيل: {str(e)}")

    def view_sales_history(self):
        """عرض تاريخ المبيعات"""
        try:
            # إنشاء نافذة تاريخ المبيعات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QLabel, QDateEdit, QComboBox
            from PyQt5.QtCore import QDate

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 تاريخ المبيعات")
            dialog.setModal(True)
            dialog.resize(900, 600)

            layout = QVBoxLayout()

            # فلاتر التاريخ
            filter_layout = QHBoxLayout()

            filter_layout.addWidget(QLabel("من تاريخ:"))
            from_date = QDateEdit()
            from_date.setDate(QDate.currentDate().addDays(-30))  # آخر 30 يوم
            from_date.setCalendarPopup(True)
            filter_layout.addWidget(from_date)

            filter_layout.addWidget(QLabel("إلى تاريخ:"))
            to_date = QDateEdit()
            to_date.setDate(QDate.currentDate())
            to_date.setCalendarPopup(True)
            filter_layout.addWidget(to_date)

            filter_layout.addWidget(QLabel("العميل:"))
            customer_filter = QComboBox()
            customer_filter.addItem("جميع العملاء")
            # إضافة العملاء من البيانات الحالية
            customers = set()
            for row in range(self.sales_table.rowCount()):
                customer_item = self.sales_table.item(row, 1)
                if customer_item:
                    customers.add(customer_item.text())
            for customer in sorted(customers):
                customer_filter.addItem(customer)
            filter_layout.addWidget(customer_filter)

            filter_button = QPushButton("🔍 تطبيق الفلتر")
            filter_layout.addWidget(filter_button)

            layout.addLayout(filter_layout)

            # جدول التاريخ
            history_table = QTableWidget()
            history_table.setColumnCount(8)
            history_table.setHorizontalHeaderLabels([
                "التاريخ", "رقم الفاتورة", "العميل", "المنتج",
                "الكمية", "سعر الوحدة", "الإجمالي", "الحالة"
            ])

            # إعداد التحديد للسطر كاملاً
            history_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            history_table.setSelectionMode(QAbstractItemView.SingleSelection)

            # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
            history_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

            # ضبط إعدادات التمرير للتحكم الدقيق
            try:
                scrollbar = history_table.verticalScrollBar()
                if scrollbar:
                    scrollbar.setSingleStep(50)
                    scrollbar.setPageStep(200)
            except Exception:
                pass

            # إضافة معالج التمرير المخصص
            def history_wheelEvent(event):
                try:
                    delta = event.angleDelta().y()
                    if abs(delta) < 120:
                        event.accept()
                        return

                    scrollbar = history_table.verticalScrollBar()
                    if not scrollbar:
                        event.accept()
                        return

                    if delta > 0:
                        scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                    else:
                        scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                    event.accept()
                except Exception:
                    QTableWidget.wheelEvent(history_table, event)

            history_table.wheelEvent = history_wheelEvent

            # تحميل البيانات
            def load_history_data():
                # محاكاة بيانات تاريخية
                history_data = []
                for row in range(self.sales_table.rowCount()):
                    row_data = []
                    for col in [2, 0, 1, 3, 4, 5, 6, 7]:  # إعادة ترتيب الأعمدة
                        item = self.sales_table.item(row, col)
                        row_data.append(item.text() if item else "")
                    history_data.append(row_data)

                # ترتيب حسب التاريخ (الأحدث أولاً)
                history_data.sort(key=lambda x: x[0], reverse=True)

                history_table.setRowCount(len(history_data))
                for row, data in enumerate(history_data):
                    for col, value in enumerate(data):
                        item = QTableWidgetItem(str(value))
                        item.setFont(QFont("Arial", 10, QFont.Bold))

                        # تلوين حسب الحالة
                        if col == 7:  # عمود الحالة
                            if value == "مكتمل":
                                item.setBackground(QColor("#d1fae5"))
                            elif value == "معلق":
                                item.setBackground(QColor("#fef3c7"))
                            elif value == "ملغي":
                                item.setBackground(QColor("#fee2e2"))

                        history_table.setItem(row, col, item)

                # تعديل عرض الأعمدة
                header = history_table.horizontalHeader()
                header.setStretchLastSection(True)
                for i in range(history_table.columnCount()):
                    header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

            # تحميل البيانات الأولية
            load_history_data()

            # ربط زر الفلتر
            filter_button.clicked.connect(load_history_data)

            layout.addWidget(history_table)

            # إحصائيات سريعة
            stats_layout = QHBoxLayout()

            total_sales = history_table.rowCount()
            total_amount = 0
            for row in range(history_table.rowCount()):
                amount_item = history_table.item(row, 6)
                if amount_item:
                    try:
                        amount = float(amount_item.text().replace(',', '').replace('جنيه', '').strip())
                        total_amount += amount
                    except:
                        pass

            stats_label = QLabel(f"📊 إجمالي المبيعات: {total_sales} | 💰 إجمالي المبلغ: {int(total_amount):,} جنيه")
            stats_label.setStyleSheet("font-weight: bold; font-size: 12px; padding: 10px; background-color: #f0f9ff; border-radius: 5px;")
            stats_layout.addWidget(stats_label)

            layout.addLayout(stats_layout)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            export_history_btn = QPushButton("📤 تصدير التاريخ")
            export_history_btn.clicked.connect(lambda: self.export_sales_history(history_table))
            buttons_layout.addWidget(export_history_btn)

            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض تاريخ المبيعات: {str(e)}")

    def export_sales_history(self, history_table):
        """تصدير تاريخ المبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تاريخ المبيعات", "تاريخ_المبيعات.csv", "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    headers = []
                    for col in range(history_table.columnCount()):
                        headers.append(history_table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(history_table.rowCount()):
                        row_data = []
                        for col in range(history_table.columnCount()):
                            item = history_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                QMessageBox.information(self, "تم", f"تم تصدير تاريخ المبيعات بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير التاريخ: {str(e)}")

    def view_customer_info(self):
        """عرض معلومات العميل"""
        selected_row = self.sales_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مبيعة من القائمة")
            return

        customer = self.sales_table.item(selected_row, 2).text() if self.sales_table.item(selected_row, 2) else ""
        QMessageBox.information(self, "معلومات العميل", f"معلومات العميل: {customer}")

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للحالات مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # إنشاء تخطيط أفقي للإطار
        filter_layout = QHBoxLayout(self.status_filter_frame)
        filter_layout.setContentsMargins(5, 0, 5, 0)
        filter_layout.setSpacing(8)

        # سهم يسار
        self.status_left_arrow = QPushButton("▼")
        self.status_left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # النص الحالي
        self.current_status_label = QLabel("جميع الحالات")
        self.current_status_label.setAlignment(Qt.AlignCenter)
        self.current_status_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.status_menu_button = QPushButton("▼")
        self.status_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # إضافة العناصر للتخطيط
        filter_layout.addWidget(self.status_left_arrow, 0)
        filter_layout.addWidget(self.current_status_label, 1)
        filter_layout.addWidget(self.status_menu_button, 0)

        # إنشاء القائمة المنسدلة للحالات
        self.status_menu = QMenu(self)
        self.status_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                margin: 2px 5px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إضافة خيارات التصفية للحالات مع أيقونات مطابقة للعملاء
        status_options = [
            ("جميع الحالات", None),
            ("🟢 مدفوعة", "مدفوعة"),
            ("🟡 معلقة", "معلقة"),
            ("🔴 ملغية", "ملغية")
        ]

        for text, value in status_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_status_filter(v, t))
            self.status_menu.addAction(action)

        # ربط الأزرار بالقائمة
        self.status_menu_button.clicked.connect(self.show_status_menu)
        self.status_left_arrow.clicked.connect(self.show_status_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.status_frame_mouse_press_event
        self.current_status_label.mousePressEvent = self.status_frame_mouse_press_event

        # تعيين القيم الافتراضية
        self.current_status_value = "all"

    def show_status_menu(self):
        """عرض قائمة تصفية الحالات"""
        try:
            button = self.sender()
            if button:
                # إذا تم استدعاؤها من زر
                self.status_menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))
            else:
                # إذا تم استدعاؤها من mousePressEvent
                self.status_menu.exec_(self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft()))
        except Exception as e:
            print(f"خطأ في عرض قائمة الحالات: {str(e)}")
            # عرض القائمة في موقع افتراضي
            self.status_menu.exec_(self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft()))

    def set_status_filter(self, value, text):
        """تعيين تصفية الحالة"""
        self.current_status_value = value
        self.current_status_label.setText(text)
        self.filter_sales()

    def status_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية الحالة"""
        self.show_status_menu()


class SaleDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل مبيعة - مطابقة للعملاء والموردين"""

    def __init__(self, parent=None, sale=None, session=None):
        super().__init__(parent)
        self.sale = sale
        self.session = session
        self.parent_widget = parent
        self.is_edit_mode = sale is not None
        self.init_ui()

        # تحميل البيانات في وضع التعديل
        if self.is_edit_mode:
            self.load_sale_data()

    def init_ui(self):
        # إعداد نافذة الحوار مطابق للعملاء والموردين
        if self.sale:
            self.setWindowTitle("🛍️ تعديل مبيعة - نظام إدارة المبيعات المتطور والشامل")
        else:
            self.setWindowTitle("🛍️ إضافة مبيعة جديدة - نظام إدارة المبيعات المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة مطابقة للنافذة الرئيسية
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        self.setModal(True)
        self.resize(700, 850)

        # إنشاء التخطيط الرئيسي مطابق للعملاء والموردين
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # عنوان النافذة مطابق للعملاء والموردين
        title_text = "تعديل بيانات المبيعة" if self.sale else "إضافة مبيعة جديدة"
        title_label = QLabel(f"🛍️ {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
                font-weight: bold;
                font-size: 20px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء نموذج معلومات المبيعة مطابق للعملاء والموردين
        form_layout = QFormLayout()
        form_layout.setSpacing(15)

        # إنشاء دالة لتصميم النصوص مع عرض مقلل - مطابقة للعملاء والموردين
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 120px;
                    max-width: 120px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # حقل العميل مطابق للعملاء والموردين
        self.client_combo = QComboBox()
        self.client_combo.addItem("-- اختر عميل --", None)
        self.client_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 15px rgba(147, 51, 234, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
                transform: scale(1.02);
            }
        """)

        # إضافة العملاء من قاعدة البيانات
        if self.session:
            clients = self.session.query(Client).all()
            for client in clients:
                self.client_combo.addItem(client.name, client.id)

        form_layout.addRow(create_styled_label("العميل", "👤", True), self.client_combo)

        # رقم المبيعة
        self.sale_number_edit = QLineEdit()
        self.sale_number_edit.setPlaceholderText("أدخل رقم المبيعة...")
        self.sale_number_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("رقم المبيعة", "🔢", True), self.sale_number_edit)

        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setStyleSheet("""
            QDateEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDateEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("التاريخ", "📅"), self.date_edit)

        # حقل المبلغ الإجمالي مطابق للعملاء والموردين
        self.total_amount_edit = QDoubleSpinBox()
        self.total_amount_edit.setRange(0, 10000000)
        self.total_amount_edit.setDecimals(2)
        self.total_amount_edit.setSingleStep(100)
        self.total_amount_edit.setSuffix(" جنيه")
        self.total_amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("المبلغ الإجمالي", "💰", True), self.total_amount_edit)

        # المبلغ المدفوع
        self.paid_amount_edit = QDoubleSpinBox()
        self.paid_amount_edit.setRange(0, 10000000)
        self.paid_amount_edit.setDecimals(2)
        self.paid_amount_edit.setSingleStep(100)
        self.paid_amount_edit.setSuffix(" جنيه")
        self.paid_amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("المبلغ المدفوع", "💵"), self.paid_amount_edit)

        # مبلغ الخصم
        self.discount_amount_edit = QDoubleSpinBox()
        self.discount_amount_edit.setRange(0, 10000000)
        self.discount_amount_edit.setDecimals(2)
        self.discount_amount_edit.setSingleStep(10)
        self.discount_amount_edit.setSuffix(" جنيه")
        self.discount_amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("مبلغ الخصم", "🏷️"), self.discount_amount_edit)

        # مبلغ الضريبة
        self.tax_amount_edit = QDoubleSpinBox()
        self.tax_amount_edit.setRange(0, 10000000)
        self.tax_amount_edit.setDecimals(2)
        self.tax_amount_edit.setSingleStep(10)
        self.tax_amount_edit.setSuffix(" جنيه")
        self.tax_amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("مبلغ الضريبة", "📊"), self.tax_amount_edit)

        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["معلق", "مكتمل", "ملغي", "مرتجع"])
        self.status_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 15px rgba(147, 51, 234, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("الحالة", "🎯"), self.status_combo)

        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقدي", "ائتمان", "تحويل بنكي", "شيك"])
        self.payment_method_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 15px rgba(147, 51, 234, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("طريقة الدفع", "💳"), self.payment_method_combo)

        # حقل الملاحظات مطابق للعملاء والموردين
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل ملاحظات حول المبيعة...")
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 60px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("الملاحظات", "📝"), self.notes_edit)

        main_layout.addLayout(form_layout)

        # أزرار التحكم - مطابقة للعملاء والموردين (ترتيب صحيح)
        buttons_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء والموردين
        save_button = QPushButton("💾 حفظ")
        self.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.save_sale)

        # زر الإلغاء مطابق للعملاء والموردين
        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        # ترتيب صحيح: الإلغاء أولاً ثم الحفظ
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def customize_title_bar(self):
        """تخصيص شريط العنوان - مطابق للعملاء والموردين"""
        try:
            # إنشاء أيقونة مخصصة للمبيعات مطابقة للعملاء والموردين
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج مطابق للعملاء والموردين
            from PyQt5.QtGui import QLinearGradient
            gradient = QLinearGradient(0, 0, 32, 32)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق
            gradient.setColorAt(0.5, QColor(147, 51, 234))  # بنفسجي
            gradient.setColorAt(1, QColor(236, 72, 153))  # وردي

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255), 2))
            painter.drawRoundedRect(2, 2, 28, 28, 6, 6)

            # إضافة رمز المبيعات
            painter.setPen(QColor(255, 255, 255))
            font = painter.font()
            font.setPointSize(16)
            font.setBold(True)
            painter.setFont(font)
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "🛍️")

            painter.end()

            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم شريط العنوان
            self.apply_advanced_title_bar_styling()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار - نسخة مبسطة لنافذة الحوار مطابقة للعملاء والموردين"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم بسيط كبديل مطابق للعملاء والموردين
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def save_sale(self):
        """حفظ بيانات المبيعة"""
        try:
            # جمع البيانات من جميع الحقول
            client_id = self.client_combo.currentData() if hasattr(self, 'client_combo') else None
            sale_number = self.sale_number_edit.text().strip() if hasattr(self, 'sale_number_edit') else ""
            date = self.date_edit.date().toPyDate() if hasattr(self, 'date_edit') else datetime.date.today()
            total_amount = self.total_amount_edit.value() if hasattr(self, 'total_amount_edit') else 0.0
            paid_amount = self.paid_amount_edit.value() if hasattr(self, 'paid_amount_edit') else 0.0
            discount_amount = self.discount_amount_edit.value() if hasattr(self, 'discount_amount_edit') else 0.0
            tax_amount = self.tax_amount_edit.value() if hasattr(self, 'tax_amount_edit') else 0.0
            status = self.status_combo.currentText() if hasattr(self, 'status_combo') else 'معلق'
            payment_method = self.payment_method_combo.currentText() if hasattr(self, 'payment_method_combo') else 'نقدي'
            notes = self.notes_edit.toPlainText().strip() if hasattr(self, 'notes_edit') else ""

            # التحقق من صحة البيانات
            if not client_id:
                show_error_message("خطأ", "يجب اختيار عميل")
                return

            if not sale_number:
                show_error_message("خطأ", "يجب إدخال رقم المبيعة")
                return

            if total_amount <= 0:
                show_error_message("خطأ", "يجب إدخال مبلغ صحيح")
                return

            # تحويل طريقة الدفع إلى الإنجليزية لقاعدة البيانات
            payment_method_map = {
                'نقدي': 'cash',
                'ائتمان': 'credit',
                'تحويل بنكي': 'bank_transfer',
                'شيك': 'check'
            }
            payment_method_en = payment_method_map.get(payment_method, 'cash')

            # تحويل الحالة إلى الإنجليزية لقاعدة البيانات
            status_map = {
                'معلق': 'pending',
                'مكتمل': 'completed',
                'ملغي': 'cancelled',
                'مرتجع': 'returned'
            }
            status_en = status_map.get(status, 'pending')

            if self.is_edit_mode and self.sale:
                # تحديث مبيعة موجودة
                self.sale.client_id = client_id
                self.sale.sale_number = sale_number
                self.sale.date = datetime.datetime.combine(date, datetime.time())
                self.sale.total_amount = total_amount
                self.sale.paid_amount = paid_amount
                self.sale.discount_amount = discount_amount
                self.sale.tax_amount = tax_amount
                self.sale.status = status_en
                self.sale.payment_method = payment_method_en
                self.sale.notes = notes or None

                message = f"تم تحديث المبيعة '{sale_number}' بنجاح!"
            else:
                # إنشاء مبيعة جديدة
                new_sale = Sale(
                    client_id=client_id,
                    sale_number=sale_number,
                    date=datetime.datetime.combine(date, datetime.time()),
                    total_amount=total_amount,
                    paid_amount=paid_amount,
                    discount_amount=discount_amount,
                    tax_amount=tax_amount,
                    status=status_en,
                    payment_method=payment_method_en,
                    notes=notes or None
                )
                self.session.add(new_sale)
                message = f"تم إضافة المبيعة '{sale_number}' بنجاح!"

            self.session.commit()
            show_info_message("نجح", message)
            self.accept()

        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ في حفظ المبيعة: {str(e)}")

    def load_sale_data(self):
        """تحميل بيانات المبيعة في وضع التعديل"""
        if not self.sale:
            return

        try:
            # تحميل العميل
            if hasattr(self, 'client_combo') and self.sale.client_id:
                index = self.client_combo.findData(self.sale.client_id)
                if index >= 0:
                    self.client_combo.setCurrentIndex(index)

            # تحميل رقم المبيعة
            if hasattr(self, 'sale_number_edit') and self.sale.sale_number:
                self.sale_number_edit.setText(self.sale.sale_number)

            # تحميل التاريخ
            if hasattr(self, 'date_edit') and self.sale.date:
                self.date_edit.setDate(QDate(self.sale.date))

            # تحميل المبلغ الإجمالي
            if hasattr(self, 'total_amount_edit'):
                self.total_amount_edit.setValue(self.sale.total_amount or 0.0)

            # تحميل المبلغ المدفوع
            if hasattr(self, 'paid_amount_edit'):
                self.paid_amount_edit.setValue(self.sale.paid_amount or 0.0)

            # تحميل مبلغ الخصم
            if hasattr(self, 'discount_amount_edit'):
                self.discount_amount_edit.setValue(self.sale.discount_amount or 0.0)

            # تحميل مبلغ الضريبة
            if hasattr(self, 'tax_amount_edit'):
                self.tax_amount_edit.setValue(self.sale.tax_amount or 0.0)

            # تحميل الحالة
            if hasattr(self, 'status_combo') and self.sale.status:
                status_map = {
                    'pending': 'معلق',
                    'completed': 'مكتمل',
                    'cancelled': 'ملغي',
                    'returned': 'مرتجع'
                }
                status_ar = status_map.get(self.sale.status, 'معلق')
                self.status_combo.setCurrentText(status_ar)

            # تحميل طريقة الدفع
            if hasattr(self, 'payment_method_combo') and self.sale.payment_method:
                payment_method_map = {
                    'cash': 'نقدي',
                    'credit': 'ائتمان',
                    'bank_transfer': 'تحويل بنكي',
                    'check': 'شيك'
                }
                payment_method_ar = payment_method_map.get(self.sale.payment_method, 'نقدي')
                self.payment_method_combo.setCurrentText(payment_method_ar)

            # تحميل الملاحظات
            if hasattr(self, 'notes_edit'):
                self.notes_edit.setPlainText(self.sale.notes or "")

        except Exception as e:
            print(f"خطأ في تحميل بيانات المبيعة: {e}")

    def get_data(self):
        """الحصول على بيانات المبيعة"""
        if self.sale:
            return {
                'client_id': self.sale.client_id,
                'total_amount': self.sale.total_amount,
                'notes': self.sale.notes,
                'date': self.sale.date if hasattr(self.sale, 'date') else datetime.now(),
                'status': self.sale.status if hasattr(self.sale, 'status') else 'pending'
            }
        return None


class SaleInfoDialog(QDialog):
    """نافذة تفاصيل المبيعة - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, sale=None):
        super().__init__(parent)
        self.sale = sale
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة نافذة المعلومات المرجعية - مطابق تماماً للعملاء"""
        # ═══════════════════════════════════════════════════════════════
        # إعدادات النافذة الأساسية - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        client_name = self.sale.client.name if self.sale.client else "عميل نقدي"
        self.setWindowTitle("💰📋 معلومات المبيعة - نظام إدارة المبيعات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setWindowIcon(self.create_window_icon())
        self.customize_title_bar()
        self.setModal(True)
        self.resize(850, 780)  # حجم محسن للعرض الأمثل - مطابق للعملاء

        # ═══════════════════════════════════════════════════════════════
        # تصميم النافذة والخلفية المرجعية - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        self.setStyleSheet(self.get_reference_styling())

        # ═══════════════════════════════════════════════════════════════
        # التخطيط الرئيسي المحسن - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # ═══════════════════════════════════════════════════════════════
        # عنوان النافذة المحسن - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        title_label = QLabel(f"💰 تفاصيل المبيعة: {client_name}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 22px;
                font-weight: bold;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 18px 25px;
                margin: 8px 0px 20px 0px;
            }
        """)
        main_layout.addWidget(title_label)

        # ═══════════════════════════════════════════════════════════════
        # منطقة التمرير المحسنة - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                background: transparent;
                padding: 5px;
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                width: 14px;
                border-radius: 7px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(168, 85, 247, 0.6));
                border-radius: 6px;
                min-height: 25px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.9),
                    stop:1 rgba(168, 85, 247, 0.8));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: transparent;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # محتوى المعلومات المحسن - مطابق للعملاء
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.1),
                    stop:0.2 rgba(30, 41, 59, 0.08),
                    stop:0.5 rgba(51, 65, 85, 0.06),
                    stop:0.8 rgba(71, 85, 105, 0.08),
                    stop:1 rgba(100, 116, 139, 0.1));
                border-radius: 12px;
                padding: 10px;
            }
        """)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(25)  # زيادة المسافة بين الأقسام لاستغلال المساحة الإضافية

        # إضافة معلومات المبيعة
        self.add_sale_info(info_layout)

        scroll_area.setWidget(info_widget)
        main_layout.addWidget(scroll_area)

        # ═══════════════════════════════════════════════════════════════
        # أزرار التحكم المحسنة - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        self.create_control_buttons(main_layout)

        # تطبيق تصميم شريط العنوان
        self.apply_advanced_title_bar_styling()

    def create_window_icon(self):
        """إنشاء أيقونة النافذة - مطابق للعملاء"""
        try:
            from PyQt5.QtGui import QIcon, QPixmap, QPainter, QBrush, QColor
            from PyQt5.QtCore import Qt

            # إنشاء أيقونة بسيطة
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setBrush(QBrush(QColor(59, 130, 246)))
            painter.drawEllipse(4, 4, 24, 24)
            painter.end()

            return QIcon(pixmap)
        except Exception as e:
            print(f"خطأ في إنشاء أيقونة النافذة: {str(e)}")
            return QIcon()

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي - مطابق تماماً للعملاء"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: 4px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """

    def add_sale_info(self, layout):
        """إضافة معلومات المبيعة إلى التخطيط - مطابق تماماً للنموذج المرجعي"""
        if not self.sale:
            return

        # قسم المعلومات الأساسية
        basic_info = [
            ("🔢 المعرف الفريد", f"#{str(self.sale.id).zfill(8)}"),
            ("🔢 رقم المبيعة", getattr(self.sale, 'sale_number', None) or "غير محدد"),
            ("🧑‍💼 العميل", self.sale.client.name if self.sale.client else "عميل نقدي"),
            ("📅 تاريخ المبيعة", self.sale.date.strftime('%Y-%m-%d') if self.sale.date else "غير محدد"),
            ("🎯 حالة المبيعة", self.get_sale_status())
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية", basic_info)

        # قسم المعلومات المالية
        financial_info = [
            ("💰 المبلغ الإجمالي", f"{getattr(self.sale, 'total_amount', 0):,.0f} جنيه"),
            ("💵 المبلغ المدفوع", f"{getattr(self.sale, 'paid_amount', 0):,.0f} جنيه"),
            ("🏷️ قيمة الخصم", f"{getattr(self.sale, 'discount_amount', 0):,.0f} جنيه"),
            ("📊 قيمة الضريبة", f"{getattr(self.sale, 'tax_amount', 0):,.0f} جنيه"),
            ("💳 طريقة الدفع", self.get_payment_method())
        ]
        self.add_info_section(layout, "💰 المعلومات المالية", financial_info)

        # قسم تفاصيل إضافية
        additional_info = [
            ("📋 الملاحظات", getattr(self.sale, 'notes', None) or "لا توجد ملاحظات"),
            ("⏰ تاريخ الإنشاء", self.sale.created_at.strftime('%Y-%m-%d %H:%M') if hasattr(self.sale, 'created_at') and self.sale.created_at else "غير محدد"),
            ("🔄 آخر تحديث", self.sale.updated_at.strftime('%Y-%m-%d %H:%M') if hasattr(self.sale, 'updated_at') and self.sale.updated_at else "غير محدد"),
            ("💼 نوع المعاملة", self.get_transaction_type()),
            ("📊 ملخص المبيعة", self.get_sale_summary())
        ]
        self.add_info_section(layout, "📊 تفاصيل إضافية", additional_info)

    def add_info_section(self, layout, title, info_list):
        """إضافة قسم معلومات مع التصميم المرجعي"""
        # إطار القسم
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(30, 41, 59, 0.15),
                    stop:0.3 rgba(51, 65, 85, 0.12),
                    stop:0.7 rgba(71, 85, 105, 0.1),
                    stop:1 rgba(100, 116, 139, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                margin: 5px;
                padding: 15px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15, 15, 15, 15)
        section_layout.setSpacing(12)

        # عنوان القسم
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(34, 197, 94, 0.2));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 10px 18px;
                margin-bottom: 12px;
            }
        """)
        section_layout.addWidget(title_label)

        # معلومات القسم
        for label_text, value_text in info_list:
            info_frame = QFrame()
            info_frame.setStyleSheet("""
                QFrame {
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 6px;
                    margin: 2px;
                    padding: 8px;
                }
            """)

            info_layout = QHBoxLayout(info_frame)
            info_layout.setContentsMargins(10, 8, 10, 8)

            # التسمية
            label = QLabel(label_text)
            label.setStyleSheet("""
                QLabel {
                    color: #E2E8F0;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 160px;
                    max-width: 160px;
                }
            """)

            # القيمة
            value = QLabel(str(value_text))
            value.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: normal;
                }
            """)
            value.setWordWrap(True)

            info_layout.addWidget(label)
            info_layout.addWidget(value)
            info_layout.addStretch()

            section_layout.addWidget(info_frame)

        layout.addWidget(section_frame)

    def get_sale_status(self):
        """حالة المبيعة"""
        status_map = {
            'pending': 'معلق ⏳',
            'completed': 'مكتمل ✅',
            'cancelled': 'ملغي ❌',
            'returned': 'مرتجع 🔄'
        }
        return status_map.get(self.sale.status, self.sale.status or 'غير محدد')

    def get_payment_method(self):
        """طريقة الدفع"""
        payment_map = {
            'cash': 'نقدي 💵',
            'credit': 'ائتمان 💳',
            'bank_transfer': 'تحويل بنكي 🏦',
            'check': 'شيك 📄'
        }
        return payment_map.get(getattr(self.sale, 'payment_method', None), 'نقدي 💵')

    def get_transaction_type(self):
        """نوع المعاملة"""
        if getattr(self.sale, 'total_amount', 0) > 10000:
            return "معاملة كبيرة 💎"
        elif getattr(self.sale, 'total_amount', 0) > 1000:
            return "معاملة متوسطة 💰"
        else:
            return "معاملة صغيرة 💵"

    def get_sale_summary(self):
        """ملخص المبيعة"""
        client_name = self.sale.client.name if self.sale.client else "عميل نقدي"
        amount = getattr(self.sale, 'total_amount', 0)
        return f"مبيعة {client_name} بقيمة {amount:,.0f} جنيه"

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم المحسنة - مطابق تماماً للنموذج المرجعي"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 10px;
                margin: 5px 0;
                min-height: 65px;
                max-height: 70px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # زر الإغلاق - في المقدمة
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(200)
        close_btn.setMaximumHeight(45)
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)
        print_btn.setMaximumHeight(45)
        self.style_advanced_button(print_btn, 'emerald')
        print_btn.clicked.connect(self.print_info)

        # زر تصدير PDF
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(200)
        export_pdf_btn.setMaximumHeight(45)
        self.style_advanced_button(export_pdf_btn, 'info')
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر إضافة ملاحظة
        note_btn = QPushButton("📝 إضافة ملاحظة")
        note_btn.setMinimumWidth(200)
        note_btn.setMaximumHeight(45)
        self.style_advanced_button(note_btn, 'orange')
        note_btn.clicked.connect(self.add_note)

        # ترتيب الأزرار
        buttons_layout.addWidget(close_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(note_btn)

        layout.addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق للنموذج المرجعي"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'emerald': ('#10b981', '#34d399'),
                    'danger': ('#ef4444', '#f87171'),
                    'info': ('#3b82f6', '#60a5fa'),
                    'orange': ('#f97316', '#fb923c')
                }

                if button_type in colors:
                    primary, secondary = colors[button_type]
                    button.setStyleSheet(f"""
                        QPushButton {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {primary}, stop:1 {secondary});
                            color: #FFFFFF;
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            border-radius: 8px;
                            font-size: 15px;
                            font-weight: bold;
                            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                            padding: 10px 14px;
                        }}
                        QPushButton:hover {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {secondary}, stop:1 {primary});
                            border: 3px solid rgba(255, 255, 255, 0.5);
                            transform: translateY(-2px);
                        }}
                        QPushButton:pressed {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {primary}, stop:1 {secondary});
                            border: 1px solid rgba(255, 255, 255, 0.2);
                            transform: translateY(1px);
                        }}
                    """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def print_info(self):
        """طباعة معلومات المبيعة"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from datetime import datetime

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y_position = 100
                line_height = 50

                # عنوان التقرير
                painter.setFont(title_font)
                client_name = self.sale.client.name if self.sale.client else "عميل نقدي"
                painter.drawText(100, y_position, f"تقرير المبيعة: {client_name}")
                y_position += line_height * 2

                # تاريخ الطباعة
                painter.setFont(normal_font)
                painter.drawText(100, y_position, f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                y_position += line_height * 2

                # المعلومات الأساسية
                painter.setFont(header_font)
                painter.drawText(100, y_position, "المعلومات الأساسية:")
                y_position += line_height

                painter.setFont(normal_font)
                basic_info = [
                    f"المعرف: #{str(self.sale.id).zfill(8)}",
                    f"رقم المبيعة: {getattr(self.sale, 'sale_number', None) or 'غير محدد'}",
                    f"العميل: {client_name}",
                    f"التاريخ: {self.sale.date.strftime('%Y-%m-%d') if self.sale.date else 'غير محدد'}",
                    f"الحالة: {self.get_sale_status()}"
                ]

                for info in basic_info:
                    painter.drawText(120, y_position, info)
                    y_position += line_height

                y_position += line_height

                # المعلومات المالية
                painter.setFont(header_font)
                painter.drawText(100, y_position, "المعلومات المالية:")
                y_position += line_height

                painter.setFont(normal_font)
                financial_info = [
                    f"المبلغ الإجمالي: {getattr(self.sale, 'total_amount', 0):,.0f} جنيه",
                    f"المبلغ المدفوع: {getattr(self.sale, 'paid_amount', 0):,.0f} جنيه",
                    f"قيمة الخصم: {getattr(self.sale, 'discount_amount', 0):,.0f} جنيه",
                    f"قيمة الضريبة: {getattr(self.sale, 'tax_amount', 0):,.0f} جنيه",
                    f"طريقة الدفع: {self.get_payment_method()}"
                ]

                for info in financial_info:
                    painter.drawText(120, y_position, info)
                    y_position += line_height

                painter.end()
                show_info_message("نجح", "تم طباعة تفاصيل المبيعة بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"خطأ في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات المبيعة إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont
            from datetime import datetime
            import os

            # اختيار مكان الحفظ
            client_name = self.sale.client.name if self.sale.client else "عميل_نقدي"
            default_filename = f"مبيعة_{client_name}_{self.sale.id}.pdf"

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير المبيعة",
                default_filename,
                "PDF Files (*.pdf)"
            )

            if file_path:
                printer = QPrinter()
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)

                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y_position = 100
                line_height = 50

                # عنوان التقرير
                painter.setFont(title_font)
                painter.drawText(100, y_position, f"تقرير المبيعة: {client_name}")
                y_position += line_height * 2

                # تاريخ التصدير
                painter.setFont(normal_font)
                painter.drawText(100, y_position, f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                y_position += line_height * 2

                # المعلومات التفصيلية
                sections = [
                    ("المعلومات الأساسية", [
                        f"المعرف: #{str(self.sale.id).zfill(8)}",
                        f"رقم المبيعة: {getattr(self.sale, 'sale_number', None) or 'غير محدد'}",
                        f"العميل: {client_name}",
                        f"التاريخ: {self.sale.date.strftime('%Y-%m-%d') if self.sale.date else 'غير محدد'}",
                        f"الحالة: {self.get_sale_status()}"
                    ]),
                    ("المعلومات المالية", [
                        f"المبلغ الإجمالي: {getattr(self.sale, 'total_amount', 0):,.0f} جنيه",
                        f"المبلغ المدفوع: {getattr(self.sale, 'paid_amount', 0):,.0f} جنيه",
                        f"قيمة الخصم: {getattr(self.sale, 'discount_amount', 0):,.0f} جنيه",
                        f"قيمة الضريبة: {getattr(self.sale, 'tax_amount', 0):,.0f} جنيه",
                        f"طريقة الدفع: {self.get_payment_method()}"
                    ]),
                    ("تفاصيل إضافية", [
                        f"الملاحظات: {getattr(self.sale, 'notes', None) or 'لا توجد ملاحظات'}",
                        f"نوع المعاملة: {self.get_transaction_type()}",
                        f"ملخص المبيعة: {self.get_sale_summary()}"
                    ])
                ]

                for section_title, section_info in sections:
                    painter.setFont(header_font)
                    painter.drawText(100, y_position, section_title + ":")
                    y_position += line_height

                    painter.setFont(normal_font)
                    for info in section_info:
                        painter.drawText(120, y_position, info)
                        y_position += line_height

                    y_position += line_height // 2

                painter.end()
                show_info_message("نجح", f"تم تصدير تقرير المبيعة إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"فشل في التصدير: {str(e)}")

    def add_note(self):
        """فتح نافذة إضافة ملاحظة متطورة"""
        try:
            dialog = AddSaleNoteDialog(self, self.sale, self.parent_widget)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_sale_info()
        except Exception as e:
            show_error_message("خطأ", f"فشل في فتح نافذة الملاحظات: {str(e)}")

    def refresh_sale_info(self):
        """تحديث معلومات المبيعة"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                updated_sale = self.parent_widget.session.query(Sale).get(self.sale.id)
                if updated_sale:
                    self.sale = updated_sale
                    self.setup_ui()
        except Exception as e:
            print(f"خطأ في تحديث معلومات المبيعة: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {str(e)}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان المتطور"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {str(e)}")


class AddSaleNoteDialog(QDialog):
    """نافذة ملاحظات المبيعة - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, sale=None, parent_widget=None):
        super().__init__(parent)
        self.sale = sale
        self.parent_widget = parent_widget
        self.setup_ui()

    def setup_ui(self):
        """إعداد نافذة بسيطة جداً - مطابق للنموذج المرجعي"""
        client_name = self.sale.client.name if self.sale.client else "عميل نقدي"
        self.setWindowTitle(f"📝 {client_name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان الخارجي ليكون أسود
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(SaleInfoDialog.get_reference_styling())

        # تخطيط بسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # عنوان داخلي مطابق للنموذج المرجعي
        title_label = QLabel(f"💰 ملاحظات المبيعة: {client_name}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 18px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 14px 22px;
                margin: 5px 0px 15px 0px;
            }
        """)
        layout.addWidget(title_label)

        # محرر النص مطابق للنموذج المرجعي
        self.text_editor = QTextEdit()
        self.text_editor.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.4),
                    stop:0.3 rgba(30, 41, 59, 0.35),
                    stop:0.7 rgba(51, 65, 85, 0.3),
                    stop:1 rgba(71, 85, 105, 0.25));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                color: #FFFFFF;
                font-size: 16px;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                padding: 14px;
                selection-background-color: rgba(59, 130, 246, 0.4);
                selection-color: #FFFFFF;
            }
            QTextEdit:focus {
                border: 3px solid rgba(59, 130, 246, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.5),
                    stop:0.3 rgba(30, 41, 59, 0.45),
                    stop:0.7 rgba(51, 65, 85, 0.4),
                    stop:1 rgba(71, 85, 105, 0.35));
            }
        """)
        self.text_editor.setPlaceholderText("اكتب ملاحظاتك هنا...\n\nيمكنك كتابة معلومات مفصلة عن المبيعة، تذكيرات، أو أي ملاحظات مهمة.")
        self.text_editor.setMinimumHeight(180)
        layout.addWidget(self.text_editor)

        # أزرار بسيطة
        self.create_buttons(layout)

        # تحميل النص
        self.load_note()

    def create_buttons(self, layout):
        """أزرار متطورة مطابقة للنموذج المرجعي"""
        # إطار الأزرار مطابق للنموذج المرجعي
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                margin: 5px 0px;
                max-height: 80px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        buttons_layout.setSpacing(20)

        # زر الإلغاء أولاً
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumWidth(140)
        cancel_btn.setMinimumHeight(45)
        self.apply_reference_button_style(cancel_btn, 'danger')
        cancel_btn.clicked.connect(self.reject)

        # زر الحفظ ثانياً
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumWidth(140)
        save_btn.setMinimumHeight(45)
        self.apply_reference_button_style(save_btn, 'success')
        save_btn.clicked.connect(self.save_note)

        # وضع الأزرار في المنتصف
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

    def apply_reference_button_style(self, button, button_type):
        """تطبيق تصميم الأزرار المرجعي المتطور - مطابق للعملاء"""
        # استخدام نفس التصميم المتطور من النموذج المرجعي
        if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(button, button_type)
        else:
            # تصميم متطور مطابق للنموذج المرجعي
            colors = {
                'success': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'border': '#10b981', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'border': '#dc2626', 'shadow': 'rgba(220, 38, 38, 0.6)'
                }
            }

            color_set = colors.get(button_type, colors['success'])

            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_set['bg_start']}, stop:0.3 {color_set['bg_mid']},
                        stop:0.7 {color_set['bg_end']}, stop:1 {color_set['bg_bottom']});
                    color: #FFFFFF;
                    border: 2px solid {color_set['border']};
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 10px 18px;
                    box-shadow: 0 4px 15px {color_set['shadow']};
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_set['hover_start']}, stop:0.3 {color_set['hover_mid']},
                        stop:0.7 {color_set['hover_end']}, stop:1 {color_set['hover_bottom']});
                    border: 3px solid {color_set['border']};
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px {color_set['shadow']};
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_set['bg_start']}, stop:0.5 {color_set['bg_mid']},
                        stop:1 {color_set['bg_end']});
                    border: 1px solid {color_set['border']};
                    transform: translateY(1px);
                    box-shadow: 0 2px 8px {color_set['shadow']};
                }}
            """)

    def load_note(self):
        """تحميل الملاحظة الحالية"""
        try:
            if self.sale and hasattr(self.sale, 'notes') and self.sale.notes:
                self.text_editor.setText(self.sale.notes)
        except Exception as e:
            print(f"خطأ في تحميل الملاحظة: {str(e)}")

    def save_note(self):
        """حفظ الملاحظة - مطابق للنموذج المرجعي"""
        try:
            note = self.text_editor.toPlainText().strip()
            self.sale.notes = note if note else None

            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                self.parent_widget.session.commit()
                if hasattr(self.parent_widget, 'refresh_data'):
                    self.parent_widget.refresh_data()

            self.accept()

            show_info_message("تم", "حُفظت الملاحظة")

        except Exception as e:
            show_error_message("خطأ", f"فشل الحفظ: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي ليكون أسود"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            # تطبيق تصميم أسود بسيط للشريط
            pass


class SalesStatisticsDialog(QDialog):
    """نافذة إحصائيات المبيعات مطابقة للعملاء والموردين والعمال والمشاريع والعقارات والمخزون"""

    def __init__(self, session, parent=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة المتطورة"""
        # إعداد النافذة الأساسي مع شريط عنوان موحد
        self.setWindowTitle("📊 إحصائيات المبيعات - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(600, 420)  # نفس ارتفاع نافذة العملاء

        # تخصيص شريط العنوان الموحد
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي المضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)  # تقليل الهوامش
        layout.setSpacing(12)  # تقليل المسافات من 20 إلى 12

        # العنوان الرئيسي المطور بدون إطار - مطابق للعملاء
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 10px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(8)

        # الأيقونة والعنوان الرئيسي
        main_title = QLabel("📊 إحصائيات المبيعات")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 30px;
                font-weight: bold;
                background: transparent;
                text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                padding: 10px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel("تقرير شامل عن حالة المبيعات والإيرادات")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # حساب الإحصائيات
        self.calculate_statistics()

        # إنشاء قائمة الإحصائيات المضغوطة
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(8)  # تقليل المسافة أكثر من 15 إلى 8
        stats_layout.setContentsMargins(20, 10, 20, 10)  # تقليل الهوامش أكثر

        # إنشاء قائمة الإحصائيات
        stats_items = [
            ("💰", "إجمالي المبيعات المسجلة", str(self.total_sales), "#3B82F6"),
            ("✅", "المبيعات المكتملة بنجاح", str(self.completed_sales), "#10B981"),
            ("⏳", "المبيعات قيد المعالجة", str(self.pending_sales), "#F59E0B"),
            ("❌", "المبيعات الملغية أو المرفوضة", str(self.cancelled_sales), "#EF4444"),
            ("💵", "إجمالي قيمة المبيعات", format_currency(self.total_amount), "#10B981")
        ]

        for icon, title, value, color in stats_items:
            # إنشاء عنصر مضغوط بدون إطارات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;  /* تقليل من 8px إلى 5px */
                    margin: 1px;   /* تقليل من 2px إلى 1px */
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(12)  # تقليل من 15 إلى 12
            item_layout.setContentsMargins(12, 5, 12, 5)  # تقليل الهوامش

            # الأيقونة بدون إطارات
            icon_label = QLabel(icon)
            icon_label.setFixedSize(28, 28)  # تصغير أكثر
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;  /* تصغير أكثر */
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 5px;
                }}
            """)
            item_layout.addWidget(icon_label)

            # العنوان المطور مع وصف مفصل
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    padding: 4px 8px;
                }
            """)
            item_layout.addWidget(title_label)

            # مساحة فارغة للدفع
            item_layout.addStretch()

            # القيمة بدون إطارات
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 6px 12px;
                    min-width: 70px;
                }}
            """)
            value_label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(value_label)

            stats_layout.addWidget(item_widget)

        layout.addLayout(stats_layout)

        # أزرار التحكم مطابقة للعملاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر تصدير PDF
        export_pdf_button = QPushButton("📄 تصدير PDF")
        export_pdf_button.clicked.connect(self.export_statistics_to_pdf)
        export_pdf_button.setMinimumHeight(45)
        self.style_advanced_button(export_pdf_button, 'info')

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        close_button.setMinimumHeight(45)
        self.style_advanced_button(close_button, 'danger')

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(export_pdf_button)

        layout.addLayout(buttons_layout)

    def calculate_statistics(self):
        """حساب إحصائيات المبيعات"""
        try:
            # حساب إجمالي المبيعات
            self.total_sales = self.session.query(Sale).count()

            # حساب المبيعات حسب الحالة (افتراض أن هناك حقل status)
            self.completed_sales = self.session.query(Sale).filter(Sale.status == 'مكتمل').count()
            self.pending_sales = self.session.query(Sale).filter(Sale.status == 'قيد المعالجة').count()
            self.cancelled_sales = self.session.query(Sale).filter(Sale.status.in_(['ملغي', 'مرفوض'])).count()

            # حساب إجمالي المبلغ
            total_amount_result = self.session.query(func.sum(Sale.total_amount)).scalar()
            self.total_amount = total_amount_result or 0

        except Exception as e:
            print(f"خطأ في حساب إحصائيات المبيعات: {e}")
            self.total_sales = 0
            self.completed_sales = 0
            self.pending_sales = 0
            self.cancelled_sales = 0
            self.total_amount = 0

    def export_statistics_to_pdf(self):
        """تصدير الإحصائيات إلى PDF - دالة مؤقتة"""
        QMessageBox.information(self, "تصدير PDF", "ميزة تصدير PDF قيد التطوير")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار مطابق للعملاء"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")

    # دوال التصدير المتقدمة الجديدة
    def export_excel_advanced(self):
        """تصدير Excel متقدم للمبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", f"مبيعات_excel_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تصدير Excel متقدم للمبيعات'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي المبيعات: {len(sales)}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    writer.writerow(['الرقم', 'رقم المبيعة', 'العميل', 'التاريخ', 'الإجمالي', 'المدفوع', 'الخصم', 'الضريبة', 'الحالة', 'طريقة الدفع', 'الملاحظات'])

                    # البيانات
                    for sale in sales:
                        client_name = sale.client.name if sale.client else 'عميل نقدي'
                        date_str = sale.date.strftime('%Y-%m-%d') if sale.date else 'غير محدد'

                        writer.writerow([
                            sale.id,
                            getattr(sale, 'sale_number', None) or 'غير محدد',
                            client_name,
                            date_str,
                            f"{getattr(sale, 'total_amount', 0):.2f}" if hasattr(sale, 'total_amount') else '0.00',
                            f"{getattr(sale, 'paid_amount', 0):.2f}" if hasattr(sale, 'paid_amount') else '0.00',
                            f"{getattr(sale, 'discount_amount', 0):.2f}" if hasattr(sale, 'discount_amount') else '0.00',
                            f"{getattr(sale, 'tax_amount', 0):.2f}" if hasattr(sale, 'tax_amount') else '0.00',
                            sale.status or 'معلق',
                            sale.payment_method or 'نقدي',
                            getattr(sale, 'notes', None) or 'لا توجد ملاحظات'
                        ])

                show_info_message("تم", f"تم تصدير Excel بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير Excel: {str(e)}")

    def export_csv_advanced(self):
        """تصدير CSV شامل للمبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV", f"مبيعات_csv_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير مع إحصائيات
                    writer.writerow(['تصدير CSV شامل للمبيعات'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])

                    # إحصائيات سريعة
                    total_amount = sum(getattr(s, 'total_amount', 0) for s in sales)
                    paid_amount = sum(getattr(s, 'paid_amount', 0) for s in sales)
                    completed_sales = len([s for s in sales if s.status == 'completed'])
                    pending_sales = len([s for s in sales if s.status == 'pending'])

                    writer.writerow([f'إجمالي المبيعات: {len(sales)}'])
                    writer.writerow([f'إجمالي المبالغ: {total_amount:.2f} جنيه'])
                    writer.writerow([f'إجمالي المدفوع: {paid_amount:.2f} جنيه'])
                    writer.writerow([f'المبيعات المكتملة: {completed_sales}'])
                    writer.writerow([f'المبيعات المعلقة: {pending_sales}'])
                    writer.writerow([])

                    # رؤوس الأعمدة الشاملة
                    writer.writerow(['الرقم', 'رقم المبيعة', 'العميل', 'هاتف العميل', 'التاريخ', 'الإجمالي', 'المدفوع', 'المتبقي', 'الخصم', 'الضريبة', 'الحالة', 'طريقة الدفع', 'الملاحظات'])

                    # البيانات الشاملة
                    for sale in sales:
                        client_name = sale.client.name if sale.client else 'عميل نقدي'
                        client_phone = sale.client.phone if sale.client and sale.client.phone else 'غير محدد'
                        date_str = sale.date.strftime('%Y-%m-%d') if sale.date else 'غير محدد'

                        total_amt = getattr(sale, 'total_amount', 0)
                        paid_amt = getattr(sale, 'paid_amount', 0)
                        remaining = total_amt - paid_amt

                        writer.writerow([
                            sale.id,
                            getattr(sale, 'sale_number', None) or 'غير محدد',
                            client_name,
                            client_phone,
                            date_str,
                            f"{total_amt:.2f}",
                            f"{paid_amt:.2f}",
                            f"{remaining:.2f}",
                            f"{getattr(sale, 'discount_amount', 0):.2f}",
                            f"{getattr(sale, 'tax_amount', 0):.2f}",
                            sale.status or 'معلق',
                            sale.payment_method or 'نقدي',
                            getattr(sale, 'notes', None) or 'لا توجد ملاحظات'
                        ])

                show_info_message("تم", f"تم تصدير CSV بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير CSV: {str(e)}")

    def export_pdf_advanced(self):
        """تصدير PDF تفصيلي للمبيعات"""
        show_info_message("قريباً", "ميزة تصدير PDF المتقدم قيد التطوير")

    def export_customer_report(self):
        """تقرير العملاء المتقدم"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv
            from sqlalchemy import func

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير العملاء", f"تقرير_عملاء_مبيعات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # تجميع البيانات حسب العميل
                customer_stats = self.session.query(
                    Sale.client_id,
                    func.count(Sale.id).label('sales_count'),
                    func.sum(Sale.total_amount).label('total_spent'),
                    func.avg(Sale.total_amount).label('avg_sale')
                ).group_by(Sale.client_id).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير العملاء المتقدم'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    writer.writerow(['اسم العميل', 'عدد المبيعات', 'إجمالي المبلغ', 'متوسط المبيعة', 'هاتف العميل'])

                    # البيانات
                    for stat in sorted(customer_stats, key=lambda x: x.total_spent or 0, reverse=True):
                        if stat.client_id:
                            client = self.session.query(Client).filter_by(id=stat.client_id).first()
                            client_name = client.name if client else 'غير محدد'
                            client_phone = client.phone if client and client.phone else 'غير محدد'
                        else:
                            client_name = 'عميل نقدي'
                            client_phone = 'غير محدد'

                        writer.writerow([
                            client_name,
                            stat.sales_count or 0,
                            f"{stat.total_spent:.2f}" if stat.total_spent else '0.00',
                            f"{stat.avg_sale:.2f}" if stat.avg_sale else '0.00',
                            client_phone
                        ])

                show_info_message("تم", f"تم إنشاء تقرير العملاء بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء تقرير العملاء: {str(e)}")

    def export_revenue_analysis(self):
        """تحليل الإيرادات المتقدم"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime, timedelta
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تحليل الإيرادات", f"تحليل_ايرادات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تحليل الإيرادات المتقدم'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # تحليل زمني
                    now = datetime.now()
                    last_month = now - timedelta(days=30)
                    last_week = now - timedelta(days=7)

                    monthly_sales = [s for s in sales if s.date and s.date >= last_month.date()]
                    weekly_sales = [s for s in sales if s.date and s.date >= last_week.date()]

                    monthly_revenue = sum(getattr(s, 'total_amount', 0) for s in monthly_sales)
                    weekly_revenue = sum(getattr(s, 'total_amount', 0) for s in weekly_sales)

                    writer.writerow(['التحليل الزمني:'])
                    writer.writerow([f'مبيعات آخر شهر: {len(monthly_sales)} بقيمة {monthly_revenue:.2f} جنيه'])
                    writer.writerow([f'مبيعات آخر أسبوع: {len(weekly_sales)} بقيمة {weekly_revenue:.2f} جنيه'])
                    writer.writerow([])

                    # تحليل طرق الدفع
                    payment_analysis = {}
                    for sale in sales:
                        method = sale.payment_method or 'نقدي'
                        if method not in payment_analysis:
                            payment_analysis[method] = {'count': 0, 'total': 0}
                        payment_analysis[method]['count'] += 1
                        payment_analysis[method]['total'] += getattr(sale, 'total_amount', 0)

                    writer.writerow(['تحليل طرق الدفع:'])
                    writer.writerow(['طريقة الدفع', 'عدد المبيعات', 'إجمالي المبلغ', 'النسبة المئوية'])

                    total_revenue = sum(data['total'] for data in payment_analysis.values())
                    for method, data in sorted(payment_analysis.items(), key=lambda x: x[1]['total'], reverse=True):
                        percentage = (data['total'] / total_revenue * 100) if total_revenue > 0 else 0
                        writer.writerow([
                            method,
                            data['count'],
                            f"{data['total']:.2f}",
                            f"{percentage:.1f}%"
                        ])

                show_info_message("تم", f"تم إنشاء تحليل الإيرادات بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تحليل الإيرادات: {str(e)}")

    def export_monthly_report(self):
        """التقرير الشهري للمبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv
            from collections import defaultdict

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير الشهري", f"تقرير_شهري_مبيعات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['التقرير الشهري للمبيعات'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # تجميع البيانات حسب الشهر
                    monthly_data = defaultdict(lambda: {'count': 0, 'total': 0, 'paid': 0})

                    for sale in sales:
                        if sale.date:
                            month_key = sale.date.strftime('%Y-%m')
                            monthly_data[month_key]['count'] += 1
                            monthly_data[month_key]['total'] += getattr(sale, 'total_amount', 0)
                            monthly_data[month_key]['paid'] += getattr(sale, 'paid_amount', 0)

                    # كتابة البيانات الشهرية
                    writer.writerow(['الشهر', 'عدد المبيعات', 'إجمالي المبلغ', 'إجمالي المدفوع', 'المتبقي', 'متوسط المبيعة'])

                    for month in sorted(monthly_data.keys(), reverse=True):
                        data = monthly_data[month]
                        remaining = data['total'] - data['paid']
                        avg_sale = data['total'] / data['count'] if data['count'] > 0 else 0

                        writer.writerow([
                            month,
                            data['count'],
                            f"{data['total']:.2f}",
                            f"{data['paid']:.2f}",
                            f"{remaining:.2f}",
                            f"{avg_sale:.2f}"
                        ])

                show_info_message("تم", f"تم إنشاء التقرير الشهري بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير الشهري: {str(e)}")

    def export_custom(self):
        """تصدير مخصص للمبيعات مع خيارات متقدمة - مطابق تماماً للعملاء"""
        show_info_message("قريباً", "ميزة التصدير المخصص قيد التطوير")

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة للمبيعات"""
        show_info_message("قريباً", "ميزة النسخ الاحتياطي قيد التطوير")

    def restore_backup(self):
        """استعادة نسخة احتياطية للمبيعات"""
        show_info_message("قريباً", "ميزة استعادة النسخ الاحتياطية قيد التطوير")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار - مطابق للأقسام الأخرى"""
        try:
            # استخدام دالة التصميم الموحدة
            UnifiedStyles.apply_advanced_button_style(button, button_type, has_menu)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")
            # تطبيق تصميم بديل بسيط
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: #3b82f6;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #2563eb;
                }}
            """)
